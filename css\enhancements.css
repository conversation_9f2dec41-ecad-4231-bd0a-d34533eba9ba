/* 新增功能样式 */

/* 特殊功能按钮 */
.filter-btn.special-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

.filter-btn.special-btn:hover {
    background: linear-gradient(45deg, #5a67d8, #6b46c1);
    border-color: transparent;
}

.filter-btn.special-btn.active {
    background: linear-gradient(45deg, #4c51bf, #553c9a);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 收藏和分享按钮 */
.bookmark-btn, .share-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.bookmark-btn:hover, .share-btn:hover {
    background: rgba(0, 82, 204, 0.1);
    transform: scale(1.1);
}

.bookmark-btn.bookmarked {
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.1); }
    28% { transform: scale(1); }
    42% { transform: scale(1.1); }
    70% { transform: scale(1); }
}

/* Toast 通知样式增强 */
.toast {
    position: fixed;
    top: 80px;
    right: 20px;
    background: linear-gradient(45deg, #0052cc, #3498db);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 10000;
    box-shadow: 0 8px 24px rgba(0, 82, 204, 0.3);
    opacity: 0;
    transition: all 0.3s ease;
    font-weight: 500;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.hide {
    opacity: 0;
    transform: translateX(100%);
}

/* 模态框增强 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
}

.modal-content {
    background: white;
    padding: 24px;
    border-radius: 12px;
    max-width: 500px;
    margin: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #333;
}

/* 地域知识卡片样式 */
.region-knowledge {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #0052cc;
    border-radius: 8px;
    padding: 20px;
    margin: 16px 0;
}

.region-knowledge h4 {
    color: #0052cc;
    margin-bottom: 12px;
    font-size: 18px;
}

.region-knowledge ul {
    list-style: none;
    padding-left: 0;
}

.region-knowledge li {
    background: white;
    margin: 8px 0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #3498db;
}

/* 热力图控制面板 */
.heatmap-controls {
    position: absolute;
    top: 60px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: none;
}

.heatmap-controls.show {
    display: block;
}

.heatmap-controls h4 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 14px;
}

.heatmap-controls label {
    display: block;
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
}

.heatmap-controls input[type="range"] {
    width: 100%;
    margin-bottom: 8px;
}

/* 连线样式增强 */
.connection-legend {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: none;
}

.connection-legend.show {
    display: block;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
}

.legend-color {
    width: 20px;
    height: 3px;
    margin-right: 8px;
    border-radius: 2px;
}

/* 数据对比功能样式 */
.compare-panel {
    position: fixed;
    right: -400px;
    top: 160px;
    width: 380px;
    height: calc(100vh - 200px);
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 8px 0 0 8px;
    box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
    transition: right 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.compare-panel.show {
    right: 0;
}

.compare-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #f8f9fa;
}

.compare-content {
    padding: 16px;
}

.compare-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    position: relative;
}

.compare-remove {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
}

/* 收藏夹面板 */
.bookmarks-panel {
    position: fixed;
    left: -400px;
    top: 160px;
    width: 380px;
    height: calc(100vh - 200px);
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 0 8px 8px 0;
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
    transition: left 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.bookmarks-panel.show {
    left: 0;
}

/* 加载动画增强 */
.loading-enhanced {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #0052cc;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e3f2fd;
    border-top: 4px solid #0052cc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-text {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
}

.loading-progress {
    width: 200px;
    height: 4px;
    background: #e3f2fd;
    border-radius: 2px;
    margin-top: 12px;
    overflow: hidden;
}

.loading-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #0052cc, #3498db);
    border-radius: 2px;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}