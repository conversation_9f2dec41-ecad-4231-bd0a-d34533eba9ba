-- 医疗专长地图用户数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS medical_map_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE medical_map_db;

-- 1. 用户基础信息表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    uuid VARCHAR(36) UNIQUE NOT NULL COMMENT '用户UUID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    user_type ENUM('patient', 'doctor', 'researcher', 'admin') NOT NULL DEFAULT 'patient' COMMENT '用户类型',
    status ENUM('active', 'inactive', 'suspended', 'deleted') NOT NULL DEFAULT 'active' COMMENT '用户状态',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
    phone VARCHAR(20) COMMENT '手机号码',
    phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机是否验证',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_last_login (last_login_at)
) ENGINE=InnoDB COMMENT='用户基础信息表';

-- 2. 用户详细资料表
CREATE TABLE user_profiles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    real_name VARCHAR(50) COMMENT '真实姓名',
    gender ENUM('male', 'female', 'other') COMMENT '性别',
    birth_date DATE COMMENT '出生日期',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    avatar_type ENUM('upload', 'initial', 'social') DEFAULT 'initial' COMMENT '头像类型',
    avatar_color VARCHAR(7) COMMENT '头像背景色',
    avatar_initial VARCHAR(2) COMMENT '头像首字母',
    bio TEXT COMMENT '个人简介',
    location VARCHAR(100) COMMENT '所在地区',
    profession VARCHAR(100) COMMENT '职业',
    specialization VARCHAR(200) COMMENT '专业领域',
    institution VARCHAR(200) COMMENT '所属机构',
    license_number VARCHAR(50) COMMENT '执业证号(医生)',
    experience_years INT COMMENT '从业年限',
    education VARCHAR(100) COMMENT '教育背景',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_location (location),
    INDEX idx_profession (profession)
) ENGINE=InnoDB COMMENT='用户详细资料表';

-- 3. 用户统计数据表
CREATE TABLE user_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    favorites_count INT DEFAULT 0 COMMENT '收藏医院数量',
    questions_asked INT DEFAULT 0 COMMENT '提问次数',
    answers_given INT DEFAULT 0 COMMENT '回答次数',
    helpful_votes INT DEFAULT 0 COMMENT '获得点赞数',
    profile_views INT DEFAULT 0 COMMENT '资料查看次数',
    last_activity_at TIMESTAMP NULL COMMENT '最后活动时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_stats (user_id),
    INDEX idx_last_activity (last_activity_at)
) ENGINE=InnoDB COMMENT='用户统计数据表';

-- 4. 用户收藏医院表
CREATE TABLE user_favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    hospital_name VARCHAR(200) NOT NULL COMMENT '医院名称',
    hospital_location VARCHAR(200) COMMENT '医院位置',
    hospital_specialty VARCHAR(100) COMMENT '医院专科',
    notes TEXT COMMENT '收藏备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_hospital_name (hospital_name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户收藏医院表';

-- 5. 用户会话表
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_token VARCHAR(128) UNIQUE NOT NULL COMMENT '会话令牌',
    device_info VARCHAR(500) COMMENT '设备信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    location VARCHAR(100) COMMENT '登录地点',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB COMMENT='用户会话表';

-- 6. 用户操作日志表
CREATE TABLE user_activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID',
    action_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    action_detail TEXT COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户操作日志表';

-- 7. 用户消息表
CREATE TABLE user_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sender_id BIGINT COMMENT '发送者ID',
    receiver_id BIGINT NOT NULL COMMENT '接收者ID',
    message_type ENUM('system', 'user', 'notification') DEFAULT 'system' COMMENT '消息类型',
    title VARCHAR(200) NOT NULL COMMENT '消息标题',
    content TEXT NOT NULL COMMENT '消息内容',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    read_at TIMESTAMP NULL COMMENT '阅读时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户消息表';

-- 8. 用户设置表
CREATE TABLE user_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    setting_key VARCHAR(100) NOT NULL COMMENT '设置键',
    setting_value TEXT COMMENT '设置值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_setting (user_id, setting_key),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB COMMENT='用户设置表';

-- 9. 实时聊天消息表
CREATE TABLE chat_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '发送用户ID',
    username VARCHAR(50) NOT NULL COMMENT '发送用户名',
    user_type ENUM('patient', 'doctor', 'researcher', 'admin') COMMENT '用户类型',
    avatar_color VARCHAR(7) COMMENT '用户头像颜色',
    avatar_initial VARCHAR(2) COMMENT '用户头像首字母',
    message TEXT NOT NULL COMMENT '消息内容',
    message_type ENUM('text', 'system', 'join', 'leave') DEFAULT 'text' COMMENT '消息类型',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_created_at (created_at),
    INDEX idx_user_id (user_id),
    INDEX idx_message_type (message_type)
) ENGINE=InnoDB COMMENT='实时聊天消息表';

-- 10. 聊天室在线用户表
CREATE TABLE chat_online_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    user_type ENUM('patient', 'doctor', 'researcher', 'admin') COMMENT '用户类型',
    avatar_color VARCHAR(7) COMMENT '用户头像颜色',
    avatar_initial VARCHAR(2) COMMENT '用户头像首字母',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE KEY uk_user_online (user_id),
    INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB COMMENT='聊天室在线用户表';
