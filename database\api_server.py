#!/usr/bin/env python3
"""
简化的API服务器
"""
import os
import sys
from flask import Flask, request, jsonify, session
from flask_cors import CORS
from datetime import datetime
import sqlite3

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

try:
    from config import db_config, user_manager
except ImportError as e:
    print(f"❌ 导入配置失败: {e}")
    sys.exit(1)

# 创建Flask应用
app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'medical-map-secret-key-2024')

# 启用CORS
CORS(app, supports_credentials=True, origins=['http://localhost:8000'])

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'success': True,
        'service': '医疗专长地图API',
        'status': 'running',
        'database': 'SQLite',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        user_type = data.get('userType', 'patient')
        
        if not all([username, email, password]):
            return jsonify({'success': False, 'message': '请填写所有必填字段'}), 400
        
        result = user_manager.create_user(username, email, password, user_type)
        
        if result['success']:
            return jsonify(result), 201
        else:
            return jsonify(result), 400
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        
        if not all([email, password]):
            return jsonify({'success': False, 'message': '请输入邮箱和密码'}), 400
        
        result = user_manager.authenticate_user(email, password)
        
        if result['success']:
            # 创建会话
            session_result = user_manager.create_session(result['user']['id'])
            if session_result['success']:
                session['session_token'] = session_result['session_token']
                return jsonify({
                    'success': True,
                    'message': '登录成功',
                    'user': result['user']
                })
            else:
                return jsonify({'success': False, 'message': '创建会话失败'}), 500
        else:
            return jsonify(result), 401
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/auth/me', methods=['GET'])
def get_current_user():
    """获取当前用户信息"""
    try:
        session_token = session.get('session_token')
        if not session_token:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        user = user_manager.get_user_by_session(session_token)
        if user:
            return jsonify({'success': True, 'user': user})
        else:
            return jsonify({'success': False, 'message': '会话已过期'}), 401
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/auth/logout', methods=['POST'])
def logout():
    """用户登出"""
    session.clear()
    return jsonify({'success': True, 'message': '登出成功'})

@app.route('/api/chat/messages', methods=['GET'])
def get_chat_messages():
    """获取聊天消息"""
    try:
        limit = request.args.get('limit', 50, type=int)
        
        conn = db_config.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, user_id, username, user_type, avatar_color, avatar_initial,
                   message, created_at
            FROM chat_messages
            ORDER BY created_at DESC
            LIMIT ?
        """, (limit,))
        
        messages = [dict(row) for row in cursor.fetchall()]
        messages.reverse()  # 最新的在底部
        
        conn.close()
        
        return jsonify({'success': True, 'messages': messages})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/chat/send', methods=['POST'])
def send_chat_message():
    """发送聊天消息"""
    try:
        session_token = session.get('session_token')
        if not session_token:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        user = user_manager.get_user_by_session(session_token)
        if not user:
            return jsonify({'success': False, 'message': '会话已过期'}), 401
        
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'success': False, 'message': '消息不能为空'}), 400
        
        if len(message) > 500:
            return jsonify({'success': False, 'message': '消息长度不能超过500字符'}), 400
        
        conn = db_config.get_connection()
        cursor = conn.cursor()
        
        # 插入聊天消息
        cursor.execute("""
            INSERT INTO chat_messages 
            (user_id, username, user_type, avatar_color, avatar_initial, message)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (user['id'], user['username'], user['user_type'],
              user['avatar_color'], user['avatar_initial'], message))
        
        message_id = cursor.lastrowid
        
        # 更新在线用户状态
        cursor.execute("""
            INSERT OR REPLACE INTO chat_online_users 
            (user_id, username, user_type, avatar_color, avatar_initial, last_seen)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (user['id'], user['username'], user['user_type'],
              user['avatar_color'], user['avatar_initial'], datetime.now().isoformat()))
        
        conn.commit()
        
        # 获取刚插入的消息
        cursor.execute("""
            SELECT id, user_id, username, user_type, avatar_color, avatar_initial,
                   message, created_at
            FROM chat_messages WHERE id = ?
        """, (message_id,))
        
        new_message = dict(cursor.fetchone())
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '消息发送成功',
            'data': new_message
        }), 201
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/chat/online', methods=['GET'])
def get_online_users():
    """获取在线用户"""
    try:
        conn = db_config.get_connection()
        cursor = conn.cursor()
        
        # 清理5分钟前的用户
        five_minutes_ago = (datetime.now() - timedelta(minutes=5)).isoformat()
        cursor.execute("DELETE FROM chat_online_users WHERE last_seen < ?", (five_minutes_ago,))
        
        # 获取在线用户
        cursor.execute("""
            SELECT username, user_type, avatar_color, avatar_initial, last_seen
            FROM chat_online_users
            ORDER BY last_seen DESC
        """)
        
        online_users = [dict(row) for row in cursor.fetchall()]
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'online_users': online_users,
            'count': len(online_users)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/users/stats', methods=['GET'])
def get_user_stats():
    """获取用户统计"""
    try:
        conn = db_config.get_connection()
        cursor = conn.cursor()
        
        # 用户统计
        cursor.execute("""
            SELECT 
                COUNT(*) as total_users,
                SUM(CASE WHEN user_type = 'patient' THEN 1 ELSE 0 END) as patients,
                SUM(CASE WHEN user_type = 'doctor' THEN 1 ELSE 0 END) as doctors,
                SUM(CASE WHEN user_type = 'researcher' THEN 1 ELSE 0 END) as researchers,
                SUM(CASE WHEN user_type = 'admin' THEN 1 ELSE 0 END) as admins
            FROM users
        """)
        
        stats = dict(cursor.fetchone())
        
        # 今日注册
        cursor.execute("""
            SELECT COUNT(*) as today_registrations
            FROM users 
            WHERE DATE(created_at) = DATE('now')
        """)
        
        today_stats = cursor.fetchone()
        stats['today_registrations'] = today_stats['today_registrations']
        
        # 本周注册
        cursor.execute("""
            SELECT COUNT(*) as week_registrations
            FROM users 
            WHERE created_at >= DATE('now', '-7 days')
        """)
        
        week_stats = cursor.fetchone()
        stats['week_registrations'] = week_stats['week_registrations']
        
        conn.close()
        
        return jsonify({'success': True, 'stats': stats})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

if __name__ == '__main__':
    port = int(os.getenv('API_PORT', 5000))
    print(f"🚀 启动API服务器 (端口: {port})")
    print(f"🗄️  数据库: SQLite ({db_config.db_path})")
    app.run(host='0.0.0.0', port=port, debug=False)
