<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地域医疗专长地图 - 发现身边的医疗专家</title>
    
    <!-- 模块化CSS文件 -->
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/header.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/hospital-cards.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="stylesheet" href="css/enhancements.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/user-status.css">
    <link rel="stylesheet" href="css/chat.css">
</head>
<body>
    <div class="bg-decoration"></div>
    
    <!-- 项目头部 -->
    <div class="gov-header">
        <div class="gov-header-content">
            <div class="gov-logo">
                <div class="gov-emblem"></div>
                <span>医疗专长地图</span>
            </div>
            <div class="gov-links">
                <a href="#about">关于项目</a>
                <a href="#contact">联系我们</a>
                <a href="https://github.com" target="_blank">GitHub</a>
                <!-- 用户状态将通过JavaScript动态添加到这里 -->
                <div class="user-status" id="userStatus"></div>
            </div>
        </div>
    </div>

    <div class="header">
        <div class="header-content">
            <h1>地域医疗专长地图</h1>
            <p>基于地理环境特色的医疗专科资源分布可视化平台</p>
        </div>
    </div>
    
    <div class="container">
        <div class="main-content">
            <div class="sidebar">
                <div class="search-section">
                    <div class="section-title">🔍 智能搜索</div>
                    <input type="text" class="search-input" placeholder="请输入医院名称、专科或地区..." id="searchInput">
                </div>

                <div class="notice-section">
                    <div class="section-title">💡 项目说明</div>
                    <div class="notice-list">
                        <div class="notice-item">
                            <span class="notice-date">理念</span>
                            <span class="notice-link">地域环境造就医疗专长，如云南擅长蘑菇中毒救治</span>
                        </div>
                        <div class="notice-item">
                            <span class="notice-date">目标</span>
                            <span class="notice-link">帮助患者找到最适合的专科医院和医疗资源</span>
                        </div>
                    </div>
                </div>

                <div class="stats-overview" id="statsOverview">
                    <div class="stat-card">
                        <div class="stat-number" id="totalHospitals">0</div>
                        <div class="stat-label">医疗机构</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalSpecialties">0</div>
                        <div class="stat-label">专科领域</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalProvinces">0</div>
                        <div class="stat-label">覆盖省份</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalCities">0</div>
                        <div class="stat-label">覆盖城市</div>
                    </div>
                </div>

                <div class="filter-section">
                    <div class="section-title">🎯 专科筛选</div>
                    <div class="filter-buttons" id="filterButtons">
                        <!-- 筛选按钮将通过JavaScript动态生成 -->
                    </div>
                </div>

                <div class="hospital-list" id="hospitalList">
                    <!-- 医院列表将通过JavaScript动态生成 -->
                </div>
            </div>

            <div class="map-container">
                <div id="map" class="loading">正在加载地图...</div>
            </div>
        </div>
    </div>

    <!-- 项目页脚 -->
    <footer class="gov-footer">
        <div class="footer-content">
            <div class="footer-sections">
                <div class="footer-section">
                    <h4>项目信息</h4>
                    <ul>
                        <li><a href="#">项目介绍</a></li>
                        <li><a href="#">数据来源</a></li>
                        <li><a href="#">使用说明</a></li>
                        <li><a href="#">版本更新</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>技术支持</h4>
                    <ul>
                        <li><a href="#">技术文档</a></li>
                        <li><a href="#">API接口</a></li>
                        <li><a href="#">问题反馈</a></li>
                        <li><a href="#">建议提交</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <ul>
                        <li>邮箱：<EMAIL></li>
                        <li>GitHub：github.com/yourname</li>
                        <li>微信：your-wechat-id</li>
                        <li>QQ：your-qq-number</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#" class="social-link">项目主页</a>
                        <a href="#" class="social-link">开源代码</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-info">
                    <p>个人开发项目 | 数据来源于公开资料整理 | 仅供学习交流使用</p>
                    <p>如有疑问请联系作者 | 持续更新维护中 ❤️</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 外部依赖 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>
    
    <!-- 模块化JavaScript文件 -->
    <script src="js/auth-manager.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/map-manager.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/chat-manager.js"></script>
    <script src="js/app.js"></script>

    <!-- Google Maps API -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY&callback=initMapCallback&language=zh-CN&libraries=visualization">
    </script>
</body>
</html>