# 医疗专长地图用户数据库设计

## 概述

这是一个完整的用户管理数据库系统，专为医疗专长地图项目设计，支持实时存储新注册用户和老用户的信息。

## 数据库架构

### 核心表结构

#### 1. users (用户基础信息表)
- **主要字段**: id, uuid, username, email, password_hash, user_type, status
- **功能**: 存储用户基本登录信息和状态
- **索引**: email, username, user_type, status, created_at

#### 2. user_profiles (用户详细资料表)
- **主要字段**: user_id, real_name, avatar_*, bio, location, profession
- **功能**: 存储用户详细个人资料
- **关联**: 外键关联users表

#### 3. user_statistics (用户统计数据表)
- **主要字段**: user_id, favorites_count, questions_asked, helpful_votes
- **功能**: 实时统计用户活动数据
- **特点**: 一对一关联，实时更新

#### 4. user_sessions (用户会话表)
- **主要字段**: user_id, session_token, device_info, expires_at
- **功能**: 管理用户登录会话，支持多设备
- **安全**: 自动过期机制

#### 5. user_activity_logs (用户操作日志表)
- **主要字段**: user_id, action_type, action_detail, ip_address
- **功能**: 记录所有用户操作，便于审计
- **特点**: 只增不删，完整记录

## 功能特性

### 🔐 安全特性
- **密码加密**: SHA256 + 随机盐值
- **会话管理**: 安全令牌 + 过期机制
- **操作日志**: 完整的用户行为记录
- **数据验证**: 严格的输入验证和类型检查

### 📊 实时统计
- **用户活动**: 收藏、提问、点赞等实时统计
- **登录追踪**: 登录次数、最后登录时间
- **设备管理**: 多设备会话管理

### 🎨 用户体验
- **头像系统**: 支持上传头像或生成首字母头像
- **个性化**: 丰富的用户资料字段
- **消息系统**: 内置用户消息功能

## 安装和使用

### 1. 环境准备
```bash
# 安装Python依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件，设置数据库连接信息
```

### 2. 数据库初始化
```bash
# 创建数据库和表结构
python manage.py create

# 或者分步执行
python manage.py init  # 仅初始化结构和数据
```

### 3. 启动API服务器
```bash
python api_server.py
```

### 4. 数据库管理
```bash
# 查看统计信息
python manage.py stats

# 备份数据库
python manage.py backup --file backup.sql

# 恢复数据库
python manage.py restore --file backup.sql

# 清理过期会话
python manage.py clean
```

## API接口

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### 统计接口
- `GET /api/users/stats` - 获取用户统计信息
- `GET /api/users/recent` - 获取最近注册用户

### 健康检查
- `GET /api/health` - 服务健康状态

## 数据库性能优化

### 索引策略
- **主键索引**: 所有表都有自增主键
- **唯一索引**: email, username, session_token
- **复合索引**: 常用查询字段组合
- **时间索引**: created_at, updated_at, expires_at

### 查询优化
- **连接池**: 使用MySQL连接池提高性能
- **预编译语句**: 防止SQL注入，提高执行效率
- **分页查询**: 大数据量查询使用LIMIT
- **视图优化**: 创建常用查询视图

## 安全考虑

### 数据安全
- **密码安全**: 不存储明文密码，使用强哈希算法
- **会话安全**: 随机令牌，定期过期
- **数据验证**: 严格的输入验证和类型检查

### 访问控制
- **用户类型**: patient, doctor, researcher, admin
- **状态管理**: active, inactive, suspended, deleted
- **权限控制**: 基于用户类型的功能权限

## 监控和维护

### 日志系统
- **API日志**: 记录所有API请求和响应
- **错误日志**: 详细的错误信息和堆栈跟踪
- **用户日志**: 用户操作行为记录

### 定期维护
- **会话清理**: 定期清理过期会话
- **日志轮转**: 防止日志文件过大
- **数据备份**: 定期自动备份数据库

## 扩展性

### 水平扩展
- **读写分离**: 支持主从数据库配置
- **缓存层**: 可集成Redis缓存
- **负载均衡**: API服务器支持多实例部署

### 功能扩展
- **第三方登录**: 预留社交登录接口
- **消息推送**: 内置消息系统可扩展推送功能
- **数据分析**: 丰富的用户行为数据支持分析

## 故障排除

### 常见问题
1. **连接失败**: 检查数据库配置和网络连接
2. **权限错误**: 确认数据库用户权限
3. **编码问题**: 确保使用UTF8MB4编码
4. **性能问题**: 检查索引和查询优化

### 调试工具
- **健康检查**: `/api/health` 接口
- **统计信息**: `python manage.py stats`
- **日志分析**: 查看logs目录下的日志文件
