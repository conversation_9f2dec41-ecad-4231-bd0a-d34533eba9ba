/* 基础样式和重置 */
@import url('https://fonts.googleapis.com/css2?family=Source+Han+Sans:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Source Han Sans', 'Microsoft YaHei', '微软雅黑', sans-serif;
    background: linear-gradient(180deg, #f5f7fa 0%, #e8eef7 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

/* 政府网站背景 */
.bg-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    background: 
        linear-gradient(45deg, rgba(0, 82, 204, 0.03) 0%, transparent 50%),
        linear-gradient(-45deg, rgba(220, 53, 69, 0.02) 0%, transparent 50%);
}

.bg-decoration::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(0, 82, 204, 0.1) 0%, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(0, 82, 204, 0.05) 0%, transparent 2px);
    background-size: 50px 50px;
    opacity: 0.3;
}

.container {
    max-width: 100vw;
    height: 100vh;
    padding: 0;
    display: flex;
    flex-direction: column;
}

/* 加载动画 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
    color: #0052cc;
    font-size: 18px;
    font-weight: 500;
}

.loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #0052cc;
    border-top: 2px solid transparent;
    border-radius: 50%;
    margin-right: 12px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header标题样式调整 - 让字体更小更紧凑 */
.header h1 {
    font-size: 1.8rem !important;
    font-weight: 700;
    margin: 0 0 4px 0;
    color: #2c3e50;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    line-height: 1.2;
}

.header p {
    font-size: 0.85rem !important;
    color: #7f8c8d;
    margin: 0;
    font-weight: 400;
    line-height: 1.3;
}

/* 让header更紧凑 */
.header {
    padding: 15px 0 !important;
}

.header-content {
    text-align: center;
}