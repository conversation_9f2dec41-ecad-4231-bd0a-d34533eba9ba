// 认证UI交互模块
class AuthUI {
    constructor() {
        this.authManager = new AuthManager();
        this.currentTab = 'login';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupAnimations();
    }

    setupEventListeners() {
        // 标签切换
        document.querySelectorAll('.auth-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabType = e.target.dataset.tab;
                this.switchTab(tabType);
            });
        });

        // 表单提交
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin(e.target);
        });

        document.getElementById('registerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister(e.target);
        });

        // 社交登录
        document.querySelector('.social-btn.wechat').addEventListener('click', () => {
            this.handleSocialLogin('wechat');
        });

        document.querySelector('.social-btn.qq').addEventListener('click', () => {
            this.handleSocialLogin('qq');
        });

        // 密码强度检查
        document.getElementById('registerPassword').addEventListener('input', (e) => {
            this.checkPasswordStrength(e.target.value);
        });

        // 实时验证
        document.querySelectorAll('input[type="email"]').forEach(input => {
            input.addEventListener('blur', (e) => {
                this.validateEmail(e.target);
            });
        });
    }

    switchTab(tabType) {
        this.currentTab = tabType;
        
        // 更新标签状态
        document.querySelectorAll('.auth-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabType}"]`).classList.add('active');

        // 切换表单
        document.querySelectorAll('.auth-form').forEach(form => {
            form.classList.add('hidden');
        });
        
        const targetForm = tabType === 'login' ? 'loginForm' : 'registerForm';
        document.getElementById(targetForm).classList.remove('hidden');

        // 更新页脚链接显示
        const footerPs = document.querySelectorAll('.auth-footer p');
        footerPs.forEach(p => p.style.display = 'none');
        
        if (tabType === 'login') {
            footerPs[0].style.display = 'block'; // 显示注册链接
        } else {
            footerPs[1].style.display = 'block'; // 显示登录链接
        }

        // 添加切换动画
        this.animateFormSwitch();
    }

    async handleLogin(form) {
        const formData = new FormData(form);
        const loginData = {
            email: formData.get('email'),
            password: formData.get('password'),
            remember: formData.get('remember') === 'on'
        };

        // 显示加载状态
        const submitBtn = form.querySelector('.auth-button');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '登录中...';
        submitBtn.disabled = true;

        try {
            const result = await this.authManager.login(loginData.email, loginData.password, loginData.remember);
            
            if (result.success) {
                this.showMessage('登录成功！', 'success');
                
                // 延迟跳转，让用户看到成功消息
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            this.showMessage('登录失败，请稍后重试', 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    async handleRegister(form) {
        const formData = new FormData(form);
        const registerData = {
            email: formData.get('email'),
            username: formData.get('username'),
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword'),
            userType: formData.get('userType'),
            agreement: formData.get('agreement') === 'on'
        };

        // 验证用户协议
        if (!registerData.agreement) {
            this.showMessage('请同意用户协议和隐私政策', 'error');
            return;
        }

        // 验证密码匹配
        if (registerData.password !== registerData.confirmPassword) {
            this.showMessage('两次输入的密码不一致', 'error');
            return;
        }

        // 验证密码强度
        if (!this.isPasswordStrong(registerData.password)) {
            this.showMessage('密码强度不足，请包含字母、数字，至少6位', 'error');
            return;
        }

        // 显示加载状态
        const submitBtn = form.querySelector('.auth-button');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '注册中...';
        submitBtn.disabled = true;

        try {
            console.log('开始注册，数据:', registerData);
            const result = await this.authManager.register(registerData);
            console.log('注册结果:', result);

            if (result.success) {
                this.showMessage('注册成功！请登录', 'success');

                // 自动切换到登录表单并填入邮箱
                setTimeout(() => {
                    this.switchTab('login');
                    document.getElementById('loginEmail').value = registerData.email;
                }, 1500);
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('注册错误:', error);
            this.showMessage(error.message || '注册失败，请稍后重试', 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    handleSocialLogin(platform) {
        this.showMessage(`${platform === 'wechat' ? '微信' : 'QQ'}登录功能开发中...`, 'info');
        
        // 这里可以集成真实的第三方登录
        // 目前作为演示功能
    }

    validateEmail(input) {
        const email = input.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            this.showFieldError(input, '请输入有效的邮箱地址');
            return false;
        } else {
            this.clearFieldError(input);
            return true;
        }
    }

    checkPasswordStrength(password) {
        const strengthIndicator = document.getElementById('password-strength') || this.createPasswordStrengthIndicator();
        const strength = this.calculatePasswordStrength(password);
        
        strengthIndicator.className = `password-strength ${strength.level}`;
        strengthIndicator.textContent = strength.text;
    }

    calculatePasswordStrength(password) {
        let score = 0;
        let feedback = [];

        if (password.length >= 6) score += 1;
        else feedback.push('至少6位');

        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('包含小写字母');

        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('包含大写字母');

        if (/[0-9]/.test(password)) score += 1;
        else feedback.push('包含数字');

        if (/[^A-Za-z0-9]/.test(password)) score += 1;
        else feedback.push('包含特殊字符');

        if (score === 0) return { level: 'weak', text: '请输入密码' };
        if (score <= 2) return { level: 'weak', text: `弱密码 (建议: ${feedback.slice(0, 2).join('、')})` };
        if (score <= 3) return { level: 'medium', text: '中等强度' };
        if (score <= 4) return { level: 'strong', text: '强密码' };
        return { level: 'very-strong', text: '非常强的密码' };
    }

    isPasswordStrong(password) {
        return password.length >= 6 && 
               /[a-zA-Z]/.test(password) && 
               /[0-9]/.test(password);
    }

    createPasswordStrengthIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'password-strength';
        indicator.className = 'password-strength';
        
        const passwordField = document.getElementById('registerPassword');
        passwordField.parentNode.appendChild(indicator);
        
        return indicator;
    }

    setupFormValidation() {
        // 实时验证用户名
        document.getElementById('registerUsername').addEventListener('input', (e) => {
            const username = e.target.value;
            if (username.length > 0 && username.length < 3) {
                this.showFieldError(e.target, '用户名至少3位');
            } else if (username.length > 20) {
                this.showFieldError(e.target, '用户名不能超过20位');
            } else {
                this.clearFieldError(e.target);
            }
        });
    }

    showFieldError(input, message) {
        this.clearFieldError(input);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        
        input.parentNode.appendChild(errorDiv);
        input.classList.add('error');
    }

    clearFieldError(input) {
        const existingError = input.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        input.classList.remove('error');
    }

    showMessage(message, type = 'info') {
        // 移除现有消息
        const existingToast = document.querySelector('.toast-message');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建新消息
        const toast = document.createElement('div');
        toast.className = `toast-message ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-icon">${this.getToastIcon(type)}</span>
                <span class="toast-text">${message}</span>
            </div>
        `;

        document.body.appendChild(toast);

        // 添加显示动画
        setTimeout(() => toast.classList.add('show'), 100);

        // 自动移除
        setTimeout(() => {
            toast.classList.add('hide');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    getToastIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    setupAnimations() {
        // 浮动医疗图标动画
        const icons = document.querySelectorAll('.medical-icon');
        icons.forEach((icon, index) => {
            const delay = index * 0.5;
            const duration = 3 + Math.random() * 2;
            
            icon.style.animationDelay = `${delay}s`;
            icon.style.animationDuration = `${duration}s`;
        });
    }

    animateFormSwitch() {
        const activeForm = document.querySelector('.auth-form:not(.hidden)');
        activeForm.style.opacity = '0';
        activeForm.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            activeForm.style.transition = 'all 0.3s ease';
            activeForm.style.opacity = '1';
            activeForm.style.transform = 'translateY(0)';
        }, 50);
    }
}

// 密码可见性切换
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.parentNode.querySelector('.password-toggle');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.textContent = '🙈';
    } else {
        input.type = 'password';
        toggle.textContent = '👁️';
    }
}

// 全局函数，供HTML调用
function switchTab(tabType) {
    if (window.authUI) {
        window.authUI.switchTab(tabType);
    }
}

// 全局函数供HTML调用
window.togglePassword = function(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.parentNode.querySelector('.password-toggle');

    if (field.type === 'password') {
        field.type = 'text';
        button.textContent = '🙈';
    } else {
        field.type = 'password';
        button.textContent = '👁️';
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.authUI = new AuthUI();
});