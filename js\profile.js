// 个人中心页面管理
class ProfileManager {
    constructor() {
        this.authManager = new AuthManager();
        this.messageManager = null;
        this.messageComposer = null;
        this.currentUser = null;
        this.currentTab = 'basic';
        this.favorites = [];
        this.messages = [];
        this.init();
    }

    init() {
        // 检查登录状态
        if (!this.authManager.isAuthenticated()) {
            window.location.href = 'login.html';
            return;
        }

        this.currentUser = this.authManager.getCurrentUser();
        
        // 初始化消息管理器
        this.messageManager = new MessageManager(this.authManager);
        this.messageComposer = new MessageComposer(this.messageManager);
        
        this.loadUserData();
        this.setupEventListeners();
        this.updateUI();
        this.initUserStatusBar();
    }

    loadUserData() {
        // 加载收藏的医院
        this.loadFavorites();
        
        // 加载消息
        this.loadMessages();
        
        // 加载用户统计数据
        this.loadUserStats();
    }

    loadFavorites() {
        const savedFavorites = localStorage.getItem(`favorites_${this.currentUser.id}`);
        if (savedFavorites) {
            try {
                this.favorites = JSON.parse(savedFavorites);
            } catch (error) {
                console.error('加载收藏数据失败:', error);
                this.favorites = [];
            }
        }
    }

    loadMessages() {
        this.messages = this.messageManager.getMessages();
    }

    loadUserStats() {
        // 从localStorage加载或计算用户统计数据
        const stats = this.currentUser.stats || {
            questionsAsked: 0,
            answersGiven: 0,
            helpfulVotes: 0,
            loginCount: 0
        };
        
        // 更新统计显示
        document.getElementById('statFavorites').textContent = this.favorites.length;
        document.getElementById('statQuestions').textContent = stats.questionsAsked;
        document.getElementById('statAnswers').textContent = stats.answersGiven;
        document.getElementById('statHelpful').textContent = stats.helpfulVotes;
    }

    setupEventListeners() {
        // 标签切换
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = e.currentTarget.dataset.tab;
                this.switchTab(tab);
            });
        });

        // 消息过滤器
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filter = e.target.dataset.filter;
                this.filterMessages(filter);
                
                // 更新按钮状态
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });

        // 设置变更监听
        document.getElementById('privacySetting').addEventListener('change', this.saveSettings.bind(this));
        document.getElementById('notificationSetting').addEventListener('change', this.saveSettings.bind(this));
    }

    updateUI() {
        // 更新个人资料显示
        this.updateProfileDisplay();
        
        // 更新收藏列表
        this.updateFavoritesList();
        
        // 更新消息列表
        this.updateMessagesList();
        
        // 更新设置
        this.updateSettings();
    }

    updateProfileDisplay() {
        const user = this.currentUser;
        
        // 头像
        const avatarHTML = this.generateAvatarHTML(user.avatar);
        document.getElementById('profileAvatar').innerHTML = avatarHTML;
        
        // 基本信息
        document.getElementById('profileName').textContent = user.username;
        document.getElementById('profileType').textContent = this.getUserTypeLabel(user.userType);
        document.getElementById('joinDate').textContent = `加入时间：${this.formatDate(user.createdAt)}`;
        document.getElementById('loginCount').textContent = `登录次数：${user.stats?.loginCount || 0}`;
        
        // 详细信息
        document.getElementById('displayUsername').textContent = user.username;
        document.getElementById('displayEmail').textContent = user.email;
        document.getElementById('displayUserType').textContent = this.getUserTypeLabel(user.userType);
        document.getElementById('displayBio').textContent = user.profile?.bio || '暂无简介';
        document.getElementById('displayLocation').textContent = user.profile?.location || '未设置';
        document.getElementById('displaySpecialties').textContent = 
            user.profile?.specialties?.join('、') || '未设置';
    }

    updateFavoritesList() {
        const container = document.getElementById('favoritesList');
        
        if (this.favorites.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🏥</div>
                    <p>还没有收藏任何医院</p>
                    <a href="index.html" class="btn-primary">去收藏医院</a>
                </div>
            `;
            return;
        }

        container.innerHTML = this.favorites.map(hospital => `
            <div class="favorite-item">
                <h4>${hospital.name}</h4>
                <p><strong>专科：</strong>${hospital.specialty}</p>
                <p><strong>地区：</strong>${hospital.province}</p>
                <p><strong>备注：</strong>${hospital.note || '暂无'}</p>
                <div class="favorite-actions">
                    <a href="index.html#hospital=${hospital.id}" class="btn-text">查看详情</a>
                    <button class="btn-text" onclick="profileManager.removeFavorite(${hospital.id})">取消收藏</button>
                </div>
            </div>
        `).join('');
    }

    updateMessagesList() {
        const container = document.getElementById('messagesList');
        
        if (this.messages.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">💬</div>
                    <p>暂无消息</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.messages.map(message => `
            <div class="message-item ${message.read ? '' : 'unread'} ${message.important ? 'important' : ''}" 
                 onclick="profileManager.viewMessage('${message.id}')">
                <div class="message-header">
                    <div class="message-sender">
                        <div class="message-avatar">
                            ${this.generateAvatarHTML(message.senderAvatar)}
                        </div>
                        <strong>${message.sender}</strong>
                        <div class="message-status">
                            <div class="status-icon ${message.read ? 'read' : 'unread'} ${message.important ? 'important' : ''} ${message.type}"></div>
                            ${message.type === 'sent' ? '已发送' : message.read ? '已读' : '未读'}
                            ${message.important ? ' · 重要' : ''}
                        </div>
                    </div>
                    <span class="message-time">${this.formatDate(message.createdAt)}</span>
                </div>
                <div class="message-content">
                    <h5>${message.subject}</h5>
                    <p>${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}</p>
                </div>
                <div class="message-actions">
                    ${message.type === 'received' ? `<button class="message-action-btn reply" onclick="event.stopPropagation(); profileManager.replyToMessage('${message.id}')">回复</button>` : ''}
                    <button class="message-action-btn delete" onclick="event.stopPropagation(); profileManager.deleteMessage('${message.id}')">删除</button>
                </div>
            </div>
        `).join('');
    }

    updateSettings() {
        const user = this.currentUser;
        document.getElementById('privacySetting').value = user.preferences?.privacy || 'public';
        document.getElementById('notificationSetting').checked = user.preferences?.notifications !== false;
    }

    switchTab(tabName) {
        // 更新菜单状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`tab-${tabName}`).classList.add('active');

        this.currentTab = tabName;
    }

    filterMessages(filter) {
        let filteredMessages = this.messageManager.getMessages(filter);
        this.renderMessages(filteredMessages);
    }

    renderMessages(messages) {
        const container = document.getElementById('messagesList');
        
        if (messages.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">💬</div>
                    <p>没有符合条件的消息</p>
                </div>
            `;
            return;
        }

        container.innerHTML = messages.map(message => `
            <div class="message-item ${message.read ? '' : 'unread'} ${message.important ? 'important' : ''}" 
                 onclick="profileManager.viewMessage('${message.id}')">
                <div class="message-header">
                    <div class="message-sender">
                        <div class="message-avatar">
                            ${this.generateAvatarHTML(message.senderAvatar)}
                        </div>
                        <strong>${message.sender}</strong>
                        <div class="message-status">
                            <div class="status-icon ${message.read ? 'read' : 'unread'} ${message.important ? 'important' : ''} ${message.type}"></div>
                            ${message.type === 'sent' ? '已发送' : message.read ? '已读' : '未读'}
                            ${message.important ? ' · 重要' : ''}
                        </div>
                    </div>
                    <span class="message-time">${this.formatDate(message.createdAt)}</span>
                </div>
                <div class="message-content">
                    <h5>${message.subject}</h5>
                    <p>${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}</p>
                </div>
                <div class="message-actions">
                    ${message.type === 'received' ? `<button class="message-action-btn reply" onclick="event.stopPropagation(); profileManager.replyToMessage('${message.id}')">回复</button>` : ''}
                    <button class="message-action-btn delete" onclick="event.stopPropagation(); profileManager.deleteMessage('${message.id}')">删除</button>
                </div>
            </div>
        `).join('');
    }

    viewMessage(messageId) {
        // 标记为已读
        this.messageManager.markAsRead(messageId);
        
        // 更新消息列表显示
        this.loadMessages();
        this.updateMessagesList();
        
        // 显示消息详情（简化处理）
        const message = this.messages.find(msg => msg.id === messageId);
        if (message) {
            this.showToast(`查看消息：${message.subject}`, 'info');
        }
    }

    replyToMessage(messageId) {
        const message = this.messages.find(msg => msg.id === messageId);
        if (message) {
            this.messageComposer.show(message.sender, 'Re: ' + message.subject);
        }
    }

    deleteMessage(messageId) {
        if (confirm('确定要删除这条消息吗？')) {
            this.messageManager.deleteMessage(messageId);
            this.loadMessages();
            this.updateMessagesList();
            this.showToast('消息已删除', 'success');
        }
    }

    removeFavorite(hospitalId) {
        this.favorites = this.favorites.filter(fav => fav.id !== hospitalId);
        localStorage.setItem(`favorites_${this.currentUser.id}`, JSON.stringify(this.favorites));
        this.updateFavoritesList();
        this.loadUserStats(); // 更新统计
        this.showToast('已取消收藏', 'success');
    }

    saveSettings() {
        const privacy = document.getElementById('privacySetting').value;
        const notifications = document.getElementById('notificationSetting').checked;
        
        const updates = {
            preferences: {
                ...this.currentUser.preferences,
                privacy: privacy,
                notifications: notifications
            }
        };
        
        this.authManager.updateProfile(updates).then(result => {
            if (result.success) {
                this.currentUser = this.authManager.getCurrentUser();
                this.showToast('设置已保存', 'success');
            }
        }).catch(error => {
            this.showToast('保存设置失败', 'error');
        });
    }

    generateAvatarHTML(avatar) {
        if (avatar.type === 'initial') {
            return `
                <div class="avatar-initial" style="background-color: ${avatar.color}">
                    ${avatar.text}
                </div>
            `;
        }
        return `<img src="${avatar.url}" alt="用户头像" class="avatar-image">`;
    }

    getUserTypeLabel(userType) {
        const labels = {
            'patient': '患者',
            'doctor': '医生',
            'researcher': '研究人员',
            'admin': '管理员'
        };
        return labels[userType] || '用户';
    }

    formatDate(dateString) {
        if (!dateString) return '--';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast-message ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-icon">${this.getToastIcon(type)}</span>
                <span class="toast-text">${message}</span>
            </div>
        `;

        document.body.appendChild(toast);

        setTimeout(() => toast.classList.add('show'), 100);
        setTimeout(() => {
            toast.classList.add('hide');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    getToastIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    initUserStatusBar() {
        const userStatus = document.getElementById('userStatus');
        if (!userStatus || !this.currentUser) return;

        const avatarHTML = this.generateAvatarHTML(this.currentUser.avatar);

        userStatus.innerHTML = `
            <div class="user-info" id="userInfo">
                ${avatarHTML}
                <span class="user-name" onclick="profileManager.toggleUserMenu()">${this.currentUser.username}</span>
            </div>
            <div class="user-menu" id="userMenu">
                <div class="user-menu-content">
                    <a href="#basic" onclick="profileManager.switchTab('basic')">
                        <span class="menu-icon">👤</span>基本信息
                    </a>
                    <a href="#activity" onclick="profileManager.switchTab('activity')">
                        <span class="menu-icon">📊</span>活动统计
                    </a>
                    <a href="#favorites" onclick="profileManager.switchTab('favorites')">
                        <span class="menu-icon">❤️</span>收藏医院
                    </a>
                    <a href="#messages" onclick="profileManager.switchTab('messages')">
                        <span class="menu-icon">💬</span>我的消息
                    </a>
                    <div class="menu-divider"></div>
                    <a href="index.html">
                        <span class="menu-icon">🏠</span>返回首页
                    </a>
                    <a href="#" onclick="authManager.logout()" class="logout-btn">
                        <span class="menu-icon">🚪</span>退出登录
                    </a>
                </div>
            </div>
        `;
    }

    toggleUserMenu() {
        const userMenu = document.getElementById('userMenu');
        if (userMenu) {
            userMenu.classList.toggle('show');
        }
    }

    generateAvatarHTML(avatar) {
        if (avatar && avatar.type === 'initial') {
            return `
                <div class="user-avatar" onclick="profileManager.toggleUserMenu()">
                    <div class="avatar-initial" style="background-color: ${avatar.color}">
                        ${avatar.text}
                    </div>
                </div>
            `;
        }
        return `
            <div class="user-avatar" onclick="profileManager.toggleUserMenu()">
                <img src="${avatar?.url || '/default-avatar.png'}" alt="用户头像" class="avatar-image">
            </div>
        `;
    }

    getUserTypeLabel(userType) {
        const labels = {
            'patient': '患者',
            'doctor': '医生',
            'researcher': '研究人员',
            'admin': '管理员'
        };
        return labels[userType] || '用户';
    }
}

// 全局函数，供HTML调用
function editProfile() {
    const user = profileManager.currentUser;
    
    // 填充表单
    document.getElementById('editUsername').value = user.username;
    document.getElementById('editBio').value = user.profile?.bio || '';
    document.getElementById('editLocation').value = user.profile?.location || '';
    document.getElementById('editSpecialties').value = user.profile?.specialties?.join('，') || '';
    
    // 显示模态框
    document.getElementById('editProfileModal').style.display = 'block';
}

function changePassword() {
    document.getElementById('changePasswordModal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    
    // 清空表单
    const form = document.querySelector(`#${modalId} form`);
    if (form) form.reset();
}

function saveProfile() {
    const form = document.getElementById('editProfileForm');
    const formData = new FormData(form);
    
    const updates = {
        username: formData.get('username'),
        profile: {
            ...profileManager.currentUser.profile,
            bio: formData.get('bio'),
            location: formData.get('location'),
            specialties: formData.get('specialties').split('，').filter(s => s.trim())
        }
    };
    
    profileManager.authManager.updateProfile(updates).then(result => {
        if (result.success) {
            profileManager.currentUser = profileManager.authManager.getCurrentUser();
            profileManager.updateProfileDisplay();
            closeModal('editProfileModal');
            profileManager.showToast('资料更新成功', 'success');
        }
    }).catch(error => {
        profileManager.showToast('更新失败：' + error.message, 'error');
    });
}

function savePassword() {
    const form = document.getElementById('changePasswordForm');
    const formData = new FormData(form);
    
    const currentPassword = formData.get('currentPassword');
    const newPassword = formData.get('newPassword');
    const confirmPassword = formData.get('confirmNewPassword');
    
    if (newPassword !== confirmPassword) {
        profileManager.showToast('两次输入的密码不一致', 'error');
        return;
    }
    
    profileManager.authManager.changePassword(currentPassword, newPassword).then(result => {
        if (result.success) {
            closeModal('changePasswordModal');
            profileManager.showToast('密码修改成功', 'success');
        }
    }).catch(error => {
        profileManager.showToast('修改失败：' + error.message, 'error');
    });
}

function composeMessage() {
    if (window.profileManager && window.profileManager.messageComposer) {
        window.profileManager.messageComposer.show();
    } else {
        console.error('消息撰写器未初始化');
    }
}

function exportData() {
    const userData = {
        profile: profileManager.currentUser,
        favorites: profileManager.favorites,
        messages: profileManager.messages,
        exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(userData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `医疗地图_用户数据_${profileManager.currentUser.username}_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    profileManager.showToast('数据导出成功', 'success');
}

function deleteAccount() {
    if (confirm('确定要删除账户吗？此操作不可恢复！')) {
        profileManager.showToast('账户删除功能开发中...', 'info');
    }
}

// 模态框点击外部关闭
window.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.profileManager = new ProfileManager();
    window.authManager = window.profileManager.authManager;
});