/* 布局样式 */
.main-content {
    max-width: 100%;
    margin: 0;
    display: grid;
    grid-template-columns: 320px 1fr;
    gap: 15px;
    padding: 15px;
    min-height: calc(100vh - 260px);
}

/* 侧边栏样式 */
.sidebar {
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 16px;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: calc(100vh - 280px);
    max-height: calc(100vh - 280px);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0052cc, #dc3545);
    border-radius: 8px 8px 0 0;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.05);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 3px;
}

/* 地图容器样式 */
.map-container {
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: calc(100vh - 280px);
    min-height: 450px;
}

.map-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0052cc, #dc3545);
    z-index: 1;
    border-radius: 8px 8px 0 0;
}

#map {
    width: 100%;
    height: 100%;
}