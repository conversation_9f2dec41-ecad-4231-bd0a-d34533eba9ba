-- 初始化数据脚本
USE medical_map_db;

-- 插入默认管理员用户
INSERT INTO users (
    uuid, username, email, password_hash, salt, user_type, status, email_verified
) VALUES (
    UUID(), 
    'admin', 
    '<EMAIL>', 
    SHA2(CONCAT('admin123', 'default_salt'), 256), 
    'default_salt', 
    'admin', 
    'active', 
    TRUE
);

-- 获取管理员用户ID
SET @admin_id = LAST_INSERT_ID();

-- 插入管理员详细资料
INSERT INTO user_profiles (
    user_id, real_name, avatar_type, avatar_color, avatar_initial, bio, profession
) VALUES (
    @admin_id, 
    '系统管理员', 
    'initial', 
    '#2196F3', 
    '管', 
    '医疗专长地图系统管理员', 
    '系统管理员'
);

-- 插入管理员统计数据
INSERT INTO user_statistics (user_id) VALUES (@admin_id);

-- 插入一些测试用户数据
INSERT INTO users (
    uuid, username, email, password_hash, salt, user_type, status, email_verified
) VALUES 
(UUID(), 'doctor_zhang', '<EMAIL>', SHA2(CONCAT('test123', 'salt1'), 256), 'salt1', 'doctor', 'active', TRUE),
(UUID(), 'patient_li', '<EMAIL>', SHA2(CONCAT('test123', 'salt2'), 256), 'salt2', 'patient', 'active', TRUE),
(UUID(), 'researcher_wang', '<EMAIL>', SHA2(CONCAT('test123', 'salt3'), 256), 'salt3', 'researcher', 'active', TRUE);

-- 获取测试用户ID
SET @doctor_id = (SELECT id FROM users WHERE username = 'doctor_zhang');
SET @patient_id = (SELECT id FROM users WHERE username = 'patient_li');
SET @researcher_id = (SELECT id FROM users WHERE username = 'researcher_wang');

-- 插入测试用户详细资料
INSERT INTO user_profiles (
    user_id, real_name, gender, avatar_type, avatar_color, avatar_initial, 
    bio, location, profession, specialization, institution
) VALUES 
(
    @doctor_id, '张医生', 'male', 'initial', '#4CAF50', '张', 
    '专注于高原病研究的资深医生', '西藏拉萨', '医生', '高原医学', '西藏自治区人民医院'
),
(
    @patient_id, '李患者', 'female', 'initial', '#FF9800', '李', 
    '关注健康的普通用户', '北京市', '教师', NULL, NULL
),
(
    @researcher_id, '王研究员', 'male', 'initial', '#9C27B0', '王', 
    '医疗地理学研究专家', '上海市', '研究员', '医疗地理学', '复旦大学公共卫生学院'
);

-- 插入用户统计数据
INSERT INTO user_statistics (
    user_id, favorites_count, questions_asked, answers_given, helpful_votes
) VALUES 
(@doctor_id, 3, 5, 15, 28),
(@patient_id, 8, 12, 2, 5),
(@researcher_id, 15, 8, 25, 45);

-- 插入一些收藏数据
INSERT INTO user_favorites (
    user_id, hospital_name, hospital_location, hospital_specialty, notes
) VALUES 
(@patient_id, '西藏自治区人民医院', '西藏拉萨', '高原病', '高原病治疗权威医院'),
(@patient_id, '云南省第一人民医院', '云南昆明', '中毒救治', '蘑菇中毒救治专业'),
(@doctor_id, '新疆医科大学第一附属医院', '新疆乌鲁木齐', '包虫病', '包虫病治疗中心');

-- 插入系统消息
INSERT INTO user_messages (
    sender_id, receiver_id, message_type, title, content
) VALUES 
(
    @admin_id, @doctor_id, 'system', '欢迎加入医疗专长地图', 
    '欢迎您注册成为医疗专长地图的医生用户，请完善您的个人资料以便为患者提供更好的服务。'
),
(
    @admin_id, @patient_id, 'system', '欢迎使用医疗专长地图', 
    '欢迎您使用医疗专长地图，您可以在这里找到最适合的专科医院和医疗资源。'
),
(
    @admin_id, @researcher_id, 'system', '研究员权限已开通', 
    '您的研究员权限已开通，可以访问更多的医疗数据和统计信息。'
);

-- 插入默认用户设置
INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES 
(@doctor_id, 'notification_email', 'true'),
(@doctor_id, 'privacy_profile', 'public'),
(@doctor_id, 'language', 'zh-CN'),
(@patient_id, 'notification_email', 'true'),
(@patient_id, 'privacy_profile', 'private'),
(@patient_id, 'language', 'zh-CN'),
(@researcher_id, 'notification_email', 'true'),
(@researcher_id, 'privacy_profile', 'public'),
(@researcher_id, 'language', 'zh-CN');

-- 创建视图：用户完整信息视图
CREATE VIEW user_full_info AS
SELECT 
    u.id,
    u.uuid,
    u.username,
    u.email,
    u.user_type,
    u.status,
    u.email_verified,
    u.phone,
    u.phone_verified,
    u.created_at,
    u.updated_at,
    u.last_login_at,
    u.login_count,
    p.real_name,
    p.gender,
    p.birth_date,
    p.avatar_url,
    p.avatar_type,
    p.avatar_color,
    p.avatar_initial,
    p.bio,
    p.location,
    p.profession,
    p.specialization,
    p.institution,
    s.favorites_count,
    s.questions_asked,
    s.answers_given,
    s.helpful_votes,
    s.profile_views,
    s.last_activity_at
FROM users u
LEFT JOIN user_profiles p ON u.id = p.user_id
LEFT JOIN user_statistics s ON u.id = s.user_id;

-- 创建存储过程：用户注册
DELIMITER //
CREATE PROCEDURE RegisterUser(
    IN p_username VARCHAR(50),
    IN p_email VARCHAR(100),
    IN p_password_hash VARCHAR(255),
    IN p_salt VARCHAR(32),
    IN p_user_type ENUM('patient', 'doctor', 'researcher', 'admin'),
    IN p_real_name VARCHAR(50),
    IN p_avatar_color VARCHAR(7),
    IN p_avatar_initial VARCHAR(2)
)
BEGIN
    DECLARE user_id BIGINT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 插入用户基础信息
    INSERT INTO users (uuid, username, email, password_hash, salt, user_type)
    VALUES (UUID(), p_username, p_email, p_password_hash, p_salt, p_user_type);
    
    SET user_id = LAST_INSERT_ID();
    
    -- 插入用户详细资料
    INSERT INTO user_profiles (
        user_id, real_name, avatar_type, avatar_color, avatar_initial
    ) VALUES (
        user_id, p_real_name, 'initial', p_avatar_color, p_avatar_initial
    );
    
    -- 插入用户统计数据
    INSERT INTO user_statistics (user_id) VALUES (user_id);
    
    -- 发送欢迎消息
    INSERT INTO user_messages (
        sender_id, receiver_id, message_type, title, content
    ) VALUES (
        1, user_id, 'system', '欢迎加入医疗专长地图', 
        '欢迎您注册成为医疗专长地图的用户，请完善您的个人资料。'
    );
    
    COMMIT;
    SELECT user_id as new_user_id;
END //
DELIMITER ;
