/* 医院卡片样式 */
.hospital-list {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 8px;
}

.hospital-list::-webkit-scrollbar {
    width: 6px;
}

.hospital-list::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.05);
    border-radius: 3px;
}

.hospital-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 3px;
}

.hospital-card {
    background: white;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e5e5e5;
    position: relative;
}

.hospital-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #0052cc;
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.hospital-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #0052cc;
}

.hospital-card:hover::before {
    transform: scaleY(1);
}

.hospital-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 16px;
    letter-spacing: -0.01em;
}

.hospital-location {
    color: #666;
    font-size: 14px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.hospital-specialty {
    background: #0052cc;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

/* 地图信息窗口 */
.info-window {
    max-width: 280px;
    font-family: 'Inter', sans-serif;
}

.info-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    font-size: 16px;
}

.info-specialty {
    background: #0052cc;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 12px;
}

.info-note {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
}

/* 悬浮详情卡片 */
.hospital-tooltip {
    position: absolute;
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    z-index: 1000;
    max-width: 300px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
}

.hospital-tooltip.show {
    opacity: 1;
    visibility: visible;
}

.tooltip-header {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
    margin-bottom: 12px;
}

.tooltip-title {
    font-weight: 600;
    color: #333;
    font-size: 16px;
    margin-bottom: 4px;
}

.tooltip-location {
    color: #666;
    font-size: 14px;
}

.tooltip-content {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
}

.tooltip-specialty-tag {
    background: linear-gradient(45deg, #0052cc, #3498db);
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    margin-right: 6px;
    margin-bottom: 6px;
    display: inline-block;
}