// 实时聊天管理模块
class ChatManager {
    constructor() {
        this.apiBaseUrl = 'http://localhost:5000/api';
        this.isOpen = false;
        this.isMinimized = false;
        this.messages = [];
        this.onlineUsers = [];
        this.currentUser = null;
        this.refreshInterval = null;
        this.onlineUsersInterval = null;
        
        this.init();
    }
    
    init() {
        this.createChatUI();
        this.bindEvents();
        
        // 检查用户登录状态
        this.checkUserStatus();
        
        // 定期刷新消息和在线用户
        this.startRefreshIntervals();
    }
    
    async checkUserStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/me`, {
                credentials: 'include'
            });
            
            const data = await response.json();
            if (data.success) {
                this.currentUser = data.user;
                this.loadMessages();
                this.loadOnlineUsers();
            }
        } catch (error) {
            console.log('用户未登录或会话已过期');
        }
    }
    
    createChatUI() {
        // 创建聊天触发按钮
        const trigger = document.createElement('button');
        trigger.className = 'chat-trigger';
        trigger.innerHTML = '💬';
        trigger.title = '打开聊天室';
        trigger.id = 'chatTrigger';
        document.body.appendChild(trigger);
        
        // 创建聊天容器
        const container = document.createElement('div');
        container.className = 'chat-container';
        container.id = 'chatContainer';
        container.style.display = 'none';
        
        container.innerHTML = `
            <div class="chat-header" id="chatHeader">
                <div class="chat-title">
                    💬 医疗交流室
                    <span class="chat-online-count" id="onlineCount">0人在线</span>
                </div>
                <button class="chat-toggle" id="chatToggle">−</button>
            </div>
            <div class="chat-online-users" id="onlineUsers" style="display: none;">
                <div>在线用户:</div>
                <div class="chat-online-users-list" id="onlineUsersList"></div>
            </div>
            <div class="chat-messages" id="chatMessages"></div>
            <div class="chat-input-container" id="chatInputContainer">
                <form class="chat-input-form" id="chatForm">
                    <input type="text" class="chat-input" id="chatInput" 
                           placeholder="输入消息..." maxlength="500" disabled>
                    <button type="submit" class="chat-send-btn" id="chatSendBtn" disabled>
                        ➤
                    </button>
                </form>
            </div>
        `;
        
        document.body.appendChild(container);
    }
    
    bindEvents() {
        const trigger = document.getElementById('chatTrigger');
        const container = document.getElementById('chatContainer');
        const header = document.getElementById('chatHeader');
        const toggle = document.getElementById('chatToggle');
        const form = document.getElementById('chatForm');
        const input = document.getElementById('chatInput');
        
        // 打开/关闭聊天室
        trigger.addEventListener('click', () => {
            this.toggleChat();
        });
        
        // 最小化/展开
        header.addEventListener('click', () => {
            this.toggleMinimize();
        });
        
        toggle.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleMinimize();
        });
        
        // 发送消息
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.sendMessage();
        });
        
        // 回车发送消息
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 点击在线用户数显示/隐藏用户列表
        document.getElementById('onlineCount').addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleOnlineUsers();
        });
    }
    
    toggleChat() {
        const trigger = document.getElementById('chatTrigger');
        const container = document.getElementById('chatContainer');
        
        if (this.isOpen) {
            container.style.display = 'none';
            trigger.style.display = 'flex';
            this.isOpen = false;
            this.stopRefreshIntervals();
        } else {
            container.style.display = 'flex';
            trigger.style.display = 'none';
            this.isOpen = true;
            this.checkUserStatus();
            this.startRefreshIntervals();
            this.scrollToBottom();
        }
    }
    
    toggleMinimize() {
        const container = document.getElementById('chatContainer');
        const toggle = document.getElementById('chatToggle');
        
        if (this.isMinimized) {
            container.classList.remove('minimized');
            toggle.textContent = '−';
            this.isMinimized = false;
            this.scrollToBottom();
        } else {
            container.classList.add('minimized');
            toggle.textContent = '+';
            this.isMinimized = true;
        }
    }
    
    toggleOnlineUsers() {
        const onlineUsers = document.getElementById('onlineUsers');
        onlineUsers.style.display = onlineUsers.style.display === 'none' ? 'block' : 'none';
    }
    
    async loadMessages() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/chat/messages?limit=30`, {
                credentials: 'include'
            });
            
            const data = await response.json();
            if (data.success) {
                this.messages = data.messages;
                this.renderMessages();
            }
        } catch (error) {
            console.error('加载聊天消息失败:', error);
        }
    }
    
    async loadOnlineUsers() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/chat/online`, {
                credentials: 'include'
            });
            
            const data = await response.json();
            if (data.success) {
                this.onlineUsers = data.online_users;
                this.updateOnlineCount(data.count);
                this.renderOnlineUsers();
            }
        } catch (error) {
            console.error('加载在线用户失败:', error);
        }
    }
    
    async sendMessage() {
        const input = document.getElementById('chatInput');
        const message = input.value.trim();
        
        if (!message || !this.currentUser) return;
        
        const sendBtn = document.getElementById('chatSendBtn');
        sendBtn.disabled = true;
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/chat/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({ message })
            });
            
            const data = await response.json();
            if (data.success) {
                input.value = '';
                // 立即添加新消息到界面
                this.messages.push(data.data);
                this.renderMessages();
                this.scrollToBottom();
            } else {
                alert(data.message);
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            alert('发送消息失败，请稍后重试');
        } finally {
            sendBtn.disabled = false;
        }
    }
    
    renderMessages() {
        const container = document.getElementById('chatMessages');
        container.innerHTML = '';
        
        this.messages.forEach(message => {
            const messageEl = this.createMessageElement(message);
            container.appendChild(messageEl);
        });
        
        this.scrollToBottom();
    }
    
    createMessageElement(message) {
        const div = document.createElement('div');
        div.className = `chat-message ${message.user_id === this.currentUser?.id ? 'own' : ''}`;
        
        const time = new Date(message.created_at).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const userTypeLabels = {
            'patient': '患者',
            'doctor': '医生',
            'researcher': '研究员',
            'admin': '管理员'
        };
        
        div.innerHTML = `
            <div class="chat-avatar" style="background-color: ${message.avatar_color}">
                ${message.avatar_initial}
            </div>
            <div class="chat-message-content">
                <div class="chat-message-header">
                    <span class="chat-username">${message.username}</span>
                    <span class="chat-user-type ${message.user_type}">${userTypeLabels[message.user_type] || '用户'}</span>
                    <span class="chat-timestamp">${time}</span>
                </div>
                <div class="chat-message-text">${this.escapeHtml(message.message)}</div>
            </div>
        `;
        
        return div;
    }
    
    renderOnlineUsers() {
        const container = document.getElementById('onlineUsersList');
        container.innerHTML = '';
        
        this.onlineUsers.forEach(user => {
            const userEl = document.createElement('div');
            userEl.className = 'chat-online-user';
            userEl.innerHTML = `
                <div class="chat-online-avatar" style="background-color: ${user.avatar_color}">
                    ${user.avatar_initial}
                </div>
                <span>${user.username}</span>
            `;
            container.appendChild(userEl);
        });
    }
    
    updateOnlineCount(count) {
        const countEl = document.getElementById('onlineCount');
        countEl.textContent = `${count}人在线`;
        
        // 启用/禁用输入框
        const input = document.getElementById('chatInput');
        const sendBtn = document.getElementById('chatSendBtn');
        
        if (this.currentUser) {
            input.disabled = false;
            input.placeholder = '输入消息...';
            sendBtn.disabled = false;
        } else {
            input.disabled = true;
            input.placeholder = '请先登录...';
            sendBtn.disabled = true;
        }
    }
    
    scrollToBottom() {
        const container = document.getElementById('chatMessages');
        container.scrollTop = container.scrollHeight;
    }
    
    startRefreshIntervals() {
        // 每3秒刷新消息
        this.refreshInterval = setInterval(() => {
            if (this.isOpen && !this.isMinimized) {
                this.loadMessages();
            }
        }, 3000);
        
        // 每10秒刷新在线用户
        this.onlineUsersInterval = setInterval(() => {
            if (this.isOpen) {
                this.loadOnlineUsers();
            }
        }, 10000);
    }
    
    stopRefreshIntervals() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
        
        if (this.onlineUsersInterval) {
            clearInterval(this.onlineUsersInterval);
            this.onlineUsersInterval = null;
        }
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // 外部调用方法：用户登录后更新状态
    updateUser(user) {
        this.currentUser = user;
        this.loadMessages();
        this.loadOnlineUsers();
    }
    
    // 外部调用方法：用户登出后清理状态
    clearUser() {
        this.currentUser = null;
        this.messages = [];
        this.onlineUsers = [];
        this.renderMessages();
        this.updateOnlineCount(0);
        this.stopRefreshIntervals();
    }
}

// 全局聊天管理器实例
window.chatManager = null;

// 页面加载完成后初始化聊天功能
document.addEventListener('DOMContentLoaded', () => {
    window.chatManager = new ChatManager();
});
