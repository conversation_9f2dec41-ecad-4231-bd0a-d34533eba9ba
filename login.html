<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 地域医疗专长地图</title>
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-background">
            <div class="floating-medical-icons">
                <div class="medical-icon">🏥</div>
                <div class="medical-icon">🩺</div>
                <div class="medical-icon">💊</div>
                <div class="medical-icon">🔬</div>
                <div class="medical-icon">🏥</div>
                <div class="medical-icon">💉</div>
            </div>
        </div>
        
        <div class="auth-card">
            <div class="auth-logo">
                <div class="logo-icon">
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="logoGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#2196F3;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#FF5722;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <circle cx="50" cy="50" r="45" fill="url(#logoGrad)" stroke="white" stroke-width="3"/>
                        <path d="M30,35 Q50,25 70,35 Q65,45 50,50 Q35,45 30,35 Z" fill="white" opacity="0.9"/>
                        <circle cx="35" cy="40" r="3" fill="#FF5722"/>
                        <circle cx="50" cy="45" r="3" fill="#4CAF50"/>
                        <circle cx="65" cy="40" r="3" fill="#2196F3"/>
                        <path d="M25,60 L75,60 M25,65 L75,65 M25,70 L75,70" stroke="white" stroke-width="2" opacity="0.7"/>
                        <text x="50" y="85" text-anchor="middle" fill="white" font-size="8" font-weight="bold">地图</text>
                    </svg>
                </div>
                <h1>医疗专长地图</h1>
                <p>发现身边的医疗专家</p>
            </div>

            <div class="auth-tabs">
                <button class="auth-tab active" data-tab="login">登录</button>
                <button class="auth-tab" data-tab="register">注册</button>
            </div>

            <!-- 登录表单 -->
            <form class="auth-form" id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">邮箱地址</label>
                    <input type="email" id="loginEmail" name="email" required placeholder="请输入邮箱地址">
                    <span class="form-icon">📧</span>
                </div>
                
                <div class="form-group">
                    <label for="loginPassword">密码</label>
                    <input type="password" id="loginPassword" name="password" required placeholder="请输入密码">
                    <span class="form-icon">🔒</span>
                    <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')">👁️</button>
                </div>

                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember">
                        <span class="checkbox-custom"></span>
                        记住我
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>

                <button type="submit" class="auth-button">登录</button>
            </form>

            <!-- 注册表单 -->
            <form class="auth-form hidden" id="registerForm">
                <div class="form-group">
                    <label for="registerEmail">邮箱地址</label>
                    <input type="email" id="registerEmail" name="email" required placeholder="请输入邮箱地址">
                    <span class="form-icon">📧</span>
                </div>

                <div class="form-group">
                    <label for="registerUsername">用户名</label>
                    <input type="text" id="registerUsername" name="username" required placeholder="请输入用户名">
                    <span class="form-icon">👤</span>
                </div>
                
                <div class="form-group">
                    <label for="registerPassword">密码</label>
                    <input type="password" id="registerPassword" name="password" required placeholder="请输入密码(至少6位)">
                    <span class="form-icon">🔒</span>
                    <button type="button" class="password-toggle" onclick="togglePassword('registerPassword')">👁️</button>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="请再次输入密码">
                    <span class="form-icon">🔒</span>
                    <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">👁️</button>
                </div>

                <div class="form-group">
                    <label for="userType">用户类型</label>
                    <select id="userType" name="userType" required>
                        <option value="">请选择用户类型</option>
                        <option value="patient">患者</option>
                        <option value="doctor">医生</option>
                        <option value="researcher">研究人员</option>
                    </select>
                    <span class="form-icon">🏷️</span>
                </div>

                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" name="agreement" required>
                        <span class="checkbox-custom"></span>
                        我同意<a href="#" class="terms-link">用户协议</a>和<a href="#" class="terms-link">隐私政策</a>
                    </label>
                </div>

                <button type="submit" class="auth-button">注册</button>
            </form>

            <div class="auth-divider">
                <span>或</span>
            </div>

            <div class="social-login">
                <button class="social-btn wechat">
                    <span class="social-icon">💬</span>
                    微信登录
                </button>
                <button class="social-btn qq">
                    <span class="social-icon">🐧</span>
                    QQ登录
                </button>
            </div>

            <div class="auth-footer">
                <p>还没有账号？<a href="#" onclick="switchTab('register')">立即注册</a></p>
                <p>已有账号？<a href="#" onclick="switchTab('login')">返回登录</a></p>
            </div>

            <div class="back-to-home">
                <a href="index.html">← 返回首页</a>
            </div>
        </div>
    </div>

    <!-- 外部依赖 -->
    <script src="js/auth-manager.js"></script>
    <script src="js/auth-ui.js"></script>
</body>
</html>