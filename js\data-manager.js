// 数据管理模块
class DataManager {
    constructor() {
        this.hospitalData = [];
        this.filteredData = [];
        this.categoryMapping = {
            '中毒': '中毒救治',
            '毒': '中毒救治',
            '高原': '高原病',
            '包虫': '包虫病',
            '职业': '职业病',
            '尘肺': '职业病',
            '海洋': '海洋生物',
            '创伤': '海洋生物',
            '烧伤': '海洋生物'
        };
        this.cityCoordinates = {
            '昆明': { lat: 25.0389, lng: 102.7183 },
            '拉萨': { lat: 29.6516, lng: 91.1149 },
            '乌鲁木齐': { lat: 43.8256, lng: 87.6168 },
            '北京': { lat: 39.9042, lng: 116.4074 },
            '上海': { lat: 31.2304, lng: 121.4737 },
            '广州': { lat: 23.1291, lng: 113.2644 },
            '武汉': { lat: 30.5928, lng: 114.3058 },
            '深圳': { lat: 22.5431, lng: 114.0579 },
            '福州': { lat: 26.0745, lng: 119.2965 },
            '南京': { lat: 32.0603, lng: 118.7969 },
            '郑州': { lat: 34.7466, lng: 113.6253 },
            '长沙': { lat: 28.2282, lng: 112.9388 },
            '沈阳': { lat: 41.8057, lng: 123.4315 },
            '大连': { lat: 38.9140, lng: 121.6147 }
        };
    }

    async loadCSVData() {
        try {
            const response = await fetch('1.csv');
            const csvText = await response.text();
            return new Promise((resolve, reject) => {
                Papa.parse(csvText, {
                    header: true,
                    skipEmptyLines: true,
                    dynamicTyping: true,
                    complete: (results) => {
                        const processedData = this.processData(results.data);
                        this.hospitalData = processedData;
                        this.filteredData = [...processedData];
                        resolve(processedData);
                    },
                    error: (error) => reject(error)
                });
            });
        } catch (error) {
            console.error('读取CSV文件失败:', error);
            return [];
        }
    }

    processData(rawData) {
        return rawData.map((row, index) => {
            // 清理数据
            const cleanRow = {};
            Object.keys(row).forEach(key => {
                cleanRow[key.trim()] = typeof row[key] === 'string' ? row[key].trim() : row[key];
            });

            // 确定专科分类
            const specialty = cleanRow['Specialty_Department_Field (专科/领域)'] || '';
            let category = '其他';
            
            for (const [keyword, cat] of Object.entries(this.categoryMapping)) {
                if (specialty.includes(keyword)) {
                    category = cat;
                    break;
                }
            }

            // 获取城市坐标
            const city = cleanRow['City (城市)'] || '';
            const coordinates = this.cityCoordinates[city] || { lat: 39.9042, lng: 116.4074 };

            return {
                id: index + 1,
                name: cleanRow['Hospital_Name (医院名称)'] || '',
                city: city,
                province: cleanRow['Province_Municipality (省/直辖市)'] || '',
                specialty: specialty,
                category: category,
                coordinates: coordinates,
                notes: cleanRow['Notes_Supporting_Evidence (备注/来源)'] || '',
                basis: cleanRow['Basis_for_Inclusion (特色类型/收录依据)'] || ''
            };
        });
    }

    getFilteredData() {
        return this.filteredData;
    }

    getAllData() {
        return this.hospitalData;
    }

    filterByCategory(category) {
        if (category === 'all') {
            this.filteredData = [...this.hospitalData];
        } else {
            this.filteredData = this.hospitalData.filter(hospital => hospital.category === category);
        }
        return this.filteredData;
    }

    filterBySearch(searchTerm) {
        const term = searchTerm.toLowerCase();
        this.filteredData = this.hospitalData.filter(hospital => 
            hospital.name.toLowerCase().includes(term) ||
            hospital.specialty.toLowerCase().includes(term) ||
            hospital.city.toLowerCase().includes(term) ||
            hospital.province.toLowerCase().includes(term)
        );
        return this.filteredData;
    }

    getStats() {
        const data = this.filteredData;
        return {
            totalHospitals: data.length,
            totalSpecialties: new Set(data.map(h => h.category)).size,
            totalProvinces: new Set(data.map(h => h.province)).size,
            totalCities: new Set(data.map(h => h.city)).size
        };
    }

    getCategories() {
        return [...new Set(this.hospitalData.map(h => h.category))];
    }
}

// 导出模块
window.DataManager = DataManager;