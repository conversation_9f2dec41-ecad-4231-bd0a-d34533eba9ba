/* 简化的用户状态样式 - 集成在gov-header中 */
.user-status {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 20px;
    position: relative;
    height: 100%;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 6px 10px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    color: white;
    height: 100%;
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.15);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
}

.user-avatar:hover {
    border-color: rgba(255, 255, 255, 0.7);
    transform: scale(1.05);
}

.avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name {
    color: white;
    font-weight: 500;
    font-size: 14px;
    line-height: 1;
}

/* 简化认证链接 */
.auth-links {
    display: flex;
    gap: 8px;
    align-items: center;
    height: 100%;
}

.login-btn, .register-btn {
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.3s ease;
}

.login-btn {
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
    background: transparent;
}

.login-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.7);
}

.register-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid transparent;
}

.register-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 简化的用户菜单 */

/* 认证链接样式 */
.auth-links {
    display: flex;
    gap: 12px;
    align-items: center;
}

.login-btn, .register-btn {
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-btn {
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.4);
    background: transparent;
}

.login-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.register-btn {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border: 2px solid transparent;
}

.register-btn:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    z-index: 1000;
    min-width: 180px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.user-menu.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.user-menu-content {
    padding: 8px 0;
}

.user-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 16px;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
    font-size: 14px;
}

.user-menu a:hover {
    background: #f8f9fa;
}

.user-menu .logout-btn {
    color: #e74c3c;
    border-top: 1px solid #f1f3f4;
}

.user-menu .logout-btn:hover {
    background: #ffebee;
}

.menu-icon {
    font-size: 16px;
    width: 18px;
    text-align: center;
}

.menu-divider {
    height: 1px;
    background: #eee;
    margin: 8px 16px;
}

/* Toast消息样式 */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    z-index: 10000;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
    max-width: 400px;
}

.toast-message.show {
    transform: translateX(0);
}

.toast-message.hide {
    transform: translateX(100%);
    opacity: 0;
}

.toast-message.success {
    border-left-color: #4caf50;
}

.toast-message.error {
    border-left-color: #f44336;
}

.toast-message.warning {
    border-left-color: #ff9800;
}

.toast-message.info {
    border-left-color: #2196f3;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toast-icon {
    font-size: 18px;
}

.toast-text {
    color: #333;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-status {
        margin-left: 12px;
    }

    .user-name {
        display: none;
    }

    .auth-links {
        gap: 6px;
    }

    .login-btn, .register-btn {
        padding: 5px 10px;
        font-size: 12px;
    }

    .user-menu {
        right: 0;
        min-width: 160px;
    }
}

@media (max-width: 480px) {
    .user-status {
        margin-left: 8px;
    }

    .user-avatar {
        width: 28px;
        height: 28px;
    }

    .avatar-initial {
        font-size: 12px;
    }

    .auth-links {
        flex-direction: column;
        gap: 4px;
    }

    .login-btn, .register-btn {
        padding: 4px 8px;
        font-size: 11px;
    }
}

/* 头部布局调整 */
.header {
    position: relative;
}

.header-content {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.header h1, .header p {
    margin: 0;
}

/* 隐藏用户状态（未登录时） */
.user-status.hidden {
    display: none;
}