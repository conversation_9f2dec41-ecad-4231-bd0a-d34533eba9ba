/* 组件样式 */
.section-title {
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
    font-size: 16px;
    border-left: 4px solid #0052cc;
    padding-left: 12px;
}

/* 搜索区域 */
.search-section {
    margin-bottom: 20px;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e5e5;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
    font-family: inherit;
}

.search-input:focus {
    outline: none;
    border-color: #0052cc;
    box-shadow: 0 0 0 3px rgba(0, 82, 204, 0.1);
}

.search-input::placeholder {
    color: #999;
}

/* 公告区域 */
.notice-section {
    margin-bottom: 20px;
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
}

.notice-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.notice-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.notice-item:last-child {
    border-bottom: none;
}

.notice-date {
    font-size: 12px;
    color: #666;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
}

.notice-link {
    color: #0052cc;
    text-decoration: none;
    font-size: 13px;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.notice-link:hover {
    color: #dc3545;
    text-decoration: underline;
}

/* 统计卡片 */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 16px;
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e5e5e5;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 600;
    color: #0052cc;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 13px;
    color: #666;
    font-weight: 400;
}

/* 筛选按钮 */
.filter-section {
    margin-bottom: 20px;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid #e5e5e5;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-family: inherit;
    color: #666;
}

.filter-btn:hover {
    border-color: #0052cc;
    color: #0052cc;
}

.filter-btn.active {
    background: #0052cc;
    border-color: #0052cc;
    color: white;
}