/* 项目头部样式 */
.gov-header {
    background: #0052cc;
    color: white;
    padding: 8px 0;
    font-size: 14px;
    border-bottom: 3px solid #dc3545;
}

.gov-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 48px;
}

.gov-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 16px;
}

.gov-emblem {
    width: 32px;
    height: 32px;
    background: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><linearGradient id="mapGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%234CAF50;stop-opacity:1" /><stop offset="50%" style="stop-color:%232196F3;stop-opacity:1" /><stop offset="100%" style="stop-color:%23FF5722;stop-opacity:1" /></linearGradient></defs><circle cx="50" cy="50" r="45" fill="url(%23mapGrad)" stroke="white" stroke-width="3"/><path d="M30,35 Q50,25 70,35 Q65,45 50,50 Q35,45 30,35 Z" fill="white" opacity="0.9"/><circle cx="35" cy="40" r="3" fill="%23FF5722"/><circle cx="50" cy="45" r="3" fill="%234CAF50"/><circle cx="65" cy="40" r="3" fill="%232196F3"/><path d="M25,60 L75,60 M25,65 L75,65 M25,70 L75,70" stroke="white" stroke-width="2" opacity="0.7"/><text x="50" y="85" text-anchor="middle" fill="white" font-size="8" font-weight="bold">地图</text></svg>') center/contain no-repeat;
}

.gov-links {
    display: flex;
    gap: 20px;
    margin-left: auto;
    align-items: center;
}

.gov-links a {
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;
}

.gov-links a:hover {
    background: rgba(255,255,255,0.15);
    transform: translateY(-1px);
}

/* 主标题头部 */
.header {
    background: white;
    border-bottom: 1px solid #e5e5e5;
    padding: 20px 0;
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #0052cc;
    margin-bottom: 12px;
    letter-spacing: 1px;
}

.header p {
    font-size: 1.1rem;
    color: #666;
    font-weight: 400;
}