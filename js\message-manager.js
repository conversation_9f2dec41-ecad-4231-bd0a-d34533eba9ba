// 消息系统管理器
class MessageManager {
    constructor(authManager) {
        this.authManager = authManager;
        this.messages = [];
        this.conversations = [];
        this.init();
    }

    init() {
        this.loadMessages();
        this.loadConversations();
    }

    // 加载消息
    loadMessages() {
        if (!this.authManager.isAuthenticated()) return;
        
        const userId = this.authManager.getCurrentUser().id;
        const savedMessages = localStorage.getItem(`messages_${userId}`);
        
        if (savedMessages) {
            try {
                this.messages = JSON.parse(savedMessages);
            } catch (error) {
                console.error('加载消息失败:', error);
                this.messages = [];
            }
        } else {
            // 创建一些示例消息
            this.createSampleMessages();
        }
    }

    // 加载对话
    loadConversations() {
        if (!this.authManager.isAuthenticated()) return;
        
        const userId = this.authManager.getCurrentUser().id;
        const savedConversations = localStorage.getItem(`conversations_${userId}`);
        
        if (savedConversations) {
            try {
                this.conversations = JSON.parse(savedConversations);
            } catch (error) {
                console.error('加载对话失败:', error);
                this.conversations = [];
            }
        }
    }

    // 保存消息
    saveMessages() {
        if (!this.authManager.isAuthenticated()) return;
        
        const userId = this.authManager.getCurrentUser().id;
        localStorage.setItem(`messages_${userId}`, JSON.stringify(this.messages));
    }

    // 保存对话
    saveConversations() {
        if (!this.authManager.isAuthenticated()) return;
        
        const userId = this.authManager.getCurrentUser().id;
        localStorage.setItem(`conversations_${userId}`, JSON.stringify(this.conversations));
    }

    // 创建示例消息
    createSampleMessages() {
        const currentUser = this.authManager.getCurrentUser();
        const sampleMessages = [
            {
                id: 'msg_1',
                type: 'received',
                sender: '系统消息',
                senderAvatar: { type: 'initial', color: '#667eea', text: '系' },
                subject: '欢迎使用医疗专长地图',
                content: '欢迎您使用医疗专长地图！您可以在这里搜索医院、收藏喜欢的医院、与其他用户交流医疗经验。如果您有任何问题，请随时联系我们。',
                createdAt: new Date().toISOString(),
                read: false,
                important: true
            },
            {
                id: 'msg_2',
                type: 'received',
                sender: '平台通知',
                senderAvatar: { type: 'initial', color: '#4CAF50', text: '通' },
                subject: '新功能上线通知',
                content: '我们很高兴地通知您，医疗专长地图新增了症状匹配功能！现在您可以输入症状，系统会为您推荐最适合的专科医院。快来体验吧！',
                createdAt: new Date(Date.now() - 86400000).toISOString(),
                read: false,
                important: false
            }
        ];

        if (currentUser.userType === 'doctor') {
            sampleMessages.push({
                id: 'msg_3',
                type: 'received',
                sender: '医生认证中心',
                senderAvatar: { type: 'initial', color: '#FF5722', text: '医' },
                subject: '医生身份认证邀请',
                content: '您好！我们注意到您注册为医生用户，为了提供更好的服务和建立用户信任，邀请您完成医生身份认证。认证后您将获得专业标识和更多权限。',
                createdAt: new Date(Date.now() - 172800000).toISOString(),
                read: true,
                important: true
            });
        }

        this.messages = sampleMessages;
        this.saveMessages();
    }

    // 发送消息
    async sendMessage(messageData) {
        if (!this.authManager.isAuthenticated()) {
            throw new Error('请先登录');
        }

        const currentUser = this.authManager.getCurrentUser();
        const newMessage = {
            id: 'msg_' + Date.now(),
            type: 'sent',
            sender: currentUser.username,
            senderAvatar: currentUser.avatar,
            recipient: messageData.recipient,
            subject: messageData.subject,
            content: messageData.content,
            createdAt: new Date().toISOString(),
            read: true, // 发送的消息标记为已读
            important: false
        };

        this.messages.unshift(newMessage);
        this.saveMessages();

        // 模拟发送到接收者（在真实应用中这会通过服务器处理）
        this.simulateMessageDelivery(newMessage);

        return { success: true, message: '消息发送成功', messageId: newMessage.id };
    }

    // 模拟消息投递
    simulateMessageDelivery(message) {
        // 在真实应用中，这里会通过服务器将消息发送给接收者
        // 现在只是模拟一个自动回复
        setTimeout(() => {
            const autoReply = {
                id: 'msg_' + Date.now(),
                type: 'received',
                sender: message.recipient || '系统自动回复',
                senderAvatar: { type: 'initial', color: '#9C27B0', text: '回' },
                subject: 'Re: ' + message.subject,
                content: '感谢您的消息！这是一个自动回复。在真实的应用中，接收者会收到您的消息并可以回复。目前这只是一个演示功能。',
                createdAt: new Date().toISOString(),
                read: false,
                important: false,
                replyTo: message.id
            };

            this.messages.unshift(autoReply);
            this.saveMessages();

            // 通知界面更新
            if (window.profileManager) {
                window.profileManager.loadMessages();
                window.profileManager.updateMessagesList();
            }
        }, 2000);
    }

    // 标记消息为已读
    markAsRead(messageId) {
        const message = this.messages.find(msg => msg.id === messageId);
        if (message) {
            message.read = true;
            this.saveMessages();
            return true;
        }
        return false;
    }

    // 删除消息
    deleteMessage(messageId) {
        const index = this.messages.findIndex(msg => msg.id === messageId);
        if (index !== -1) {
            this.messages.splice(index, 1);
            this.saveMessages();
            return true;
        }
        return false;
    }

    // 获取未读消息数量
    getUnreadCount() {
        return this.messages.filter(msg => !msg.read && msg.type === 'received').length;
    }

    // 获取消息列表
    getMessages(filter = 'all') {
        switch (filter) {
            case 'unread':
                return this.messages.filter(msg => !msg.read);
            case 'sent':
                return this.messages.filter(msg => msg.type === 'sent');
            case 'important':
                return this.messages.filter(msg => msg.important);
            default:
                return this.messages;
        }
    }

    // 搜索消息
    searchMessages(query) {
        const lowerQuery = query.toLowerCase();
        return this.messages.filter(msg => 
            msg.subject.toLowerCase().includes(lowerQuery) ||
            msg.content.toLowerCase().includes(lowerQuery) ||
            msg.sender.toLowerCase().includes(lowerQuery)
        );
    }

    // 创建新对话
    createConversation(participants, subject) {
        const currentUser = this.authManager.getCurrentUser();
        const conversation = {
            id: 'conv_' + Date.now(),
            participants: [currentUser.id, ...participants],
            subject: subject,
            messages: [],
            createdAt: new Date().toISOString(),
            lastActivity: new Date().toISOString(),
            createdBy: currentUser.id
        };

        this.conversations.unshift(conversation);
        this.saveConversations();
        return conversation;
    }

    // 向对话添加消息
    addMessageToConversation(conversationId, messageContent) {
        const conversation = this.conversations.find(conv => conv.id === conversationId);
        if (!conversation) {
            throw new Error('对话不存在');
        }

        const currentUser = this.authManager.getCurrentUser();
        const message = {
            id: 'convmsg_' + Date.now(),
            senderId: currentUser.id,
            senderName: currentUser.username,
            content: messageContent,
            createdAt: new Date().toISOString(),
            read: false
        };

        conversation.messages.push(message);
        conversation.lastActivity = new Date().toISOString();
        this.saveConversations();

        return message;
    }

    // 获取对话历史
    getConversation(conversationId) {
        return this.conversations.find(conv => conv.id === conversationId);
    }

    // 获取所有对话
    getConversations() {
        return this.conversations.sort((a, b) => 
            new Date(b.lastActivity) - new Date(a.lastActivity)
        );
    }
}

// 消息撰写器
class MessageComposer {
    constructor(messageManager) {
        this.messageManager = messageManager;
        this.modal = null;
        this.form = null;
        this.setupModal();
    }

    setupModal() {
        // 创建消息撰写模态框
        const modalHTML = `
            <div class="modal" id="composeMessageModal">
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3>发送消息</h3>
                        <button class="modal-close" onclick="closeComposeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="composeMessageForm">
                            <div class="form-group">
                                <label for="messageRecipient">收件人</label>
                                <input type="text" id="messageRecipient" name="recipient" 
                                       placeholder="输入用户名或选择联系人" required>
                                <div class="recipient-suggestions" id="recipientSuggestions"></div>
                            </div>
                            <div class="form-group">
                                <label for="messageSubject">主题</label>
                                <input type="text" id="messageSubject" name="subject" 
                                       placeholder="消息主题" required>
                            </div>
                            <div class="form-group">
                                <label for="messageContent">内容</label>
                                <textarea id="messageContent" name="content" rows="6" 
                                         placeholder="输入消息内容..." required></textarea>
                                <div class="character-count">
                                    <span id="charCount">0</span>/1000
                                </div>
                            </div>
                            <div class="form-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="messageImportant">
                                    <span class="checkbox-custom"></span>
                                    标记为重要
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn-secondary" onclick="closeComposeModal()">取消</button>
                        <button class="btn-primary" onclick="sendMessage()">发送</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('composeMessageModal');
        this.form = document.getElementById('composeMessageForm');
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 字符计数
        const contentInput = document.getElementById('messageContent');
        const charCount = document.getElementById('charCount');
        
        contentInput.addEventListener('input', () => {
            const count = contentInput.value.length;
            charCount.textContent = count;
            
            if (count > 1000) {
                charCount.style.color = '#e74c3c';
            } else if (count > 800) {
                charCount.style.color = '#f39c12';
            } else {
                charCount.style.color = '#666';
            }
        });

        // 收件人建议
        const recipientInput = document.getElementById('messageRecipient');
        const suggestions = document.getElementById('recipientSuggestions');
        
        recipientInput.addEventListener('input', () => {
            const query = recipientInput.value.toLowerCase();
            if (query.length > 0) {
                this.showRecipientSuggestions(query);
            } else {
                suggestions.style.display = 'none';
            }
        });

        // 点击外部关闭建议
        document.addEventListener('click', (e) => {
            if (!recipientInput.contains(e.target) && !suggestions.contains(e.target)) {
                suggestions.style.display = 'none';
            }
        });
    }

    showRecipientSuggestions(query) {
        // 模拟用户搜索
        const mockUsers = [
            { id: 'user1', username: '张医生', type: 'doctor', avatar: { type: 'initial', color: '#4CAF50', text: '张' } },
            { id: 'user2', username: '李护士', type: 'nurse', avatar: { type: 'initial', color: '#2196F3', text: '李' } },
            { id: 'user3', username: '王研究员', type: 'researcher', avatar: { type: 'initial', color: '#FF9800', text: '王' } },
            { id: 'user4', username: '系统管理员', type: 'admin', avatar: { type: 'initial', color: '#9C27B0', text: '管' } }
        ];

        const filtered = mockUsers.filter(user => 
            user.username.toLowerCase().includes(query)
        );

        const suggestions = document.getElementById('recipientSuggestions');
        
        if (filtered.length > 0) {
            suggestions.innerHTML = filtered.map(user => `
                <div class="suggestion-item" onclick="selectRecipient('${user.username}')">
                    <div class="suggestion-avatar">
                        <div class="avatar-initial" style="background-color: ${user.avatar.color}; width: 30px; height: 30px; font-size: 12px;">
                            ${user.avatar.text}
                        </div>
                    </div>
                    <div class="suggestion-info">
                        <div class="suggestion-name">${user.username}</div>
                        <div class="suggestion-type">${this.getUserTypeLabel(user.type)}</div>
                    </div>
                </div>
            `).join('');
            suggestions.style.display = 'block';
        } else {
            suggestions.style.display = 'none';
        }
    }

    getUserTypeLabel(type) {
        const labels = {
            doctor: '医生',
            nurse: '护士',
            patient: '患者',
            researcher: '研究员',
            admin: '管理员'
        };
        return labels[type] || '用户';
    }

    show(recipient = '', subject = '') {
        // 重置表单
        this.form.reset();
        document.getElementById('charCount').textContent = '0';
        
        // 预填信息
        if (recipient) {
            document.getElementById('messageRecipient').value = recipient;
        }
        if (subject) {
            document.getElementById('messageSubject').value = subject;
        }
        
        // 显示模态框
        this.modal.style.display = 'block';
        
        // 聚焦到内容区
        setTimeout(() => {
            if (!recipient) {
                document.getElementById('messageRecipient').focus();
            } else {
                document.getElementById('messageContent').focus();
            }
        }, 100);
    }

    hide() {
        this.modal.style.display = 'none';
        document.getElementById('recipientSuggestions').style.display = 'none';
    }

    async send() {
        const formData = new FormData(this.form);
        const messageData = {
            recipient: formData.get('recipient'),
            subject: formData.get('subject'),
            content: formData.get('content'),
            important: document.getElementById('messageImportant').checked
        };

        // 验证
        if (!messageData.recipient || !messageData.subject || !messageData.content) {
            this.showError('请填写所有必填字段');
            return;
        }

        if (messageData.content.length > 1000) {
            this.showError('消息内容不能超过1000字符');
            return;
        }

        try {
            const result = await this.messageManager.sendMessage(messageData);
            if (result.success) {
                this.hide();
                this.showSuccess('消息发送成功！');
                
                // 更新消息列表
                if (window.profileManager) {
                    window.profileManager.loadMessages();
                    window.profileManager.updateMessagesList();
                }
            }
        } catch (error) {
            this.showError('发送失败：' + error.message);
        }
    }

    showError(message) {
        if (window.profileManager) {
            window.profileManager.showToast(message, 'error');
        }
    }

    showSuccess(message) {
        if (window.profileManager) {
            window.profileManager.showToast(message, 'success');
        }
    }
}

// 全局函数
function selectRecipient(username) {
    document.getElementById('messageRecipient').value = username;
    document.getElementById('recipientSuggestions').style.display = 'none';
}

function closeComposeModal() {
    if (window.messageComposer) {
        window.messageComposer.hide();
    }
}

function sendMessage() {
    if (window.messageComposer) {
        window.messageComposer.send();
    }
}

// 导出模块
window.MessageManager = MessageManager;
window.MessageComposer = MessageComposer;