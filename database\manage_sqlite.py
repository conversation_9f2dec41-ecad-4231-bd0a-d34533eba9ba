#!/usr/bin/env python3
"""
SQLite数据库管理工具
用于初始化数据库、备份、恢复等操作
"""
import os
import sys
import argparse
import sqlite3
import shutil
from datetime import datetime
import json

class SQLiteDatabaseManager:
    """SQLite数据库管理器"""
    
    def __init__(self):
        self.db_path = os.path.join(os.path.dirname(__file__), 'medical_map.db')
        self.schema_path = os.path.join(os.path.dirname(__file__), 'schema_sqlite.sql')
    
    def create_database(self):
        """创建数据库和表结构"""
        try:
            if not os.path.exists(self.schema_path):
                print(f"❌ 找不到数据库结构文件: {self.schema_path}")
                return False
            
            # 读取SQL文件
            with open(self.schema_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 连接数据库并执行SQL
            conn = sqlite3.connect(self.db_path)
            conn.executescript(sql_content)
            conn.close()
            
            print(f"✅ SQLite数据库创建成功: {self.db_path}")
            return True
            
        except Exception as e:
            print(f"❌ 创建数据库失败: {e}")
            return False
    
    def backup_database(self, backup_path=None):
        """备份数据库"""
        if not os.path.exists(self.db_path):
            print(f"❌ 数据库文件不存在: {self.db_path}")
            return False
        
        if not backup_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"backup_medical_map_{timestamp}.db"
        
        try:
            shutil.copy2(self.db_path, backup_path)
            print(f"✅ 数据库备份成功: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ 数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_path):
        """恢复数据库"""
        if not os.path.exists(backup_path):
            print(f"❌ 备份文件不存在: {backup_path}")
            return False
        
        try:
            # 备份当前数据库
            if os.path.exists(self.db_path):
                current_backup = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(self.db_path, current_backup)
                print(f"📦 当前数据库已备份到: {current_backup}")
            
            # 恢复数据库
            shutil.copy2(backup_path, self.db_path)
            print(f"✅ 数据库恢复成功: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ 数据库恢复失败: {e}")
            return False
    
    def show_stats(self):
        """显示数据库统计信息"""
        if not os.path.exists(self.db_path):
            print(f"❌ 数据库文件不存在: {self.db_path}")
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 用户统计
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN user_type = 'patient' THEN 1 ELSE 0 END) as patients,
                    SUM(CASE WHEN user_type = 'doctor' THEN 1 ELSE 0 END) as doctors,
                    SUM(CASE WHEN user_type = 'researcher' THEN 1 ELSE 0 END) as researchers,
                    SUM(CASE WHEN user_type = 'admin' THEN 1 ELSE 0 END) as admins
                FROM users WHERE status = 'active'
            """)
            user_stats = cursor.fetchone()
            
            # 今日注册统计
            cursor.execute("""
                SELECT COUNT(*) as today_registrations
                FROM users 
                WHERE DATE(created_at) = DATE('now')
            """)
            today_stats = cursor.fetchone()
            
            # 活跃会话统计
            cursor.execute("""
                SELECT COUNT(*) as active_sessions
                FROM user_sessions 
                WHERE is_active = 1 AND expires_at > datetime('now')
            """)
            session_stats = cursor.fetchone()
            
            # 聊天消息统计
            cursor.execute("""
                SELECT COUNT(*) as total_messages
                FROM chat_messages 
                WHERE is_deleted = 0
            """)
            message_stats = cursor.fetchone()
            
            # 在线用户统计
            cursor.execute("""
                SELECT COUNT(*) as online_users
                FROM chat_online_users 
                WHERE last_seen > datetime('now', '-5 minutes')
            """)
            online_stats = cursor.fetchone()
            
            print("\n=== SQLite数据库统计信息 ===")
            print(f"数据库文件: {self.db_path}")
            print(f"文件大小: {os.path.getsize(self.db_path) / 1024:.2f} KB")
            print(f"总用户数: {user_stats[0]}")
            print(f"  - 患者: {user_stats[1]}")
            print(f"  - 医生: {user_stats[2]}")
            print(f"  - 研究员: {user_stats[3]}")
            print(f"  - 管理员: {user_stats[4]}")
            print(f"今日注册: {today_stats[0]}")
            print(f"活跃会话: {session_stats[0]}")
            print(f"聊天消息: {message_stats[0]}")
            print(f"在线用户: {online_stats[0]}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
    
    def clean_expired_sessions(self):
        """清理过期会话"""
        if not os.path.exists(self.db_path):
            print(f"❌ 数据库文件不存在: {self.db_path}")
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清理过期会话
            cursor.execute("""
                DELETE FROM user_sessions 
                WHERE expires_at < datetime('now') OR is_active = 0
            """)
            
            deleted_sessions = cursor.rowcount
            
            # 清理过期在线用户
            cursor.execute("""
                DELETE FROM chat_online_users 
                WHERE last_seen < datetime('now', '-5 minutes')
            """)
            
            deleted_online = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            print(f"✅ 清理完成:")
            print(f"  - 过期会话: {deleted_sessions} 条")
            print(f"  - 离线用户: {deleted_online} 条")
            
        except Exception as e:
            print(f"❌ 清理过期数据失败: {e}")
    
    def export_data(self, export_path=None):
        """导出数据为JSON格式"""
        if not os.path.exists(self.db_path):
            print(f"❌ 数据库文件不存在: {self.db_path}")
            return False
        
        if not export_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_path = f"export_medical_map_{timestamp}.json"
        
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            data = {}
            
            # 导出用户数据
            cursor.execute("SELECT * FROM users")
            data['users'] = [dict(row) for row in cursor.fetchall()]
            
            # 导出聊天消息
            cursor.execute("SELECT * FROM chat_messages WHERE is_deleted = 0")
            data['chat_messages'] = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            
            # 写入JSON文件
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 数据导出成功: {export_path}")
            return True
            
        except Exception as e:
            print(f"❌ 数据导出失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='SQLite数据库管理工具')
    parser.add_argument('command', choices=[
        'create', 'backup', 'restore', 'stats', 'clean', 'export'
    ], help='要执行的命令')
    parser.add_argument('--file', help='备份/恢复/导出文件路径')
    
    args = parser.parse_args()
    
    manager = SQLiteDatabaseManager()
    
    print("🏥 医疗专长地图 - SQLite数据库管理工具")
    print("=" * 50)
    
    if args.command == 'create':
        print("📦 创建数据库和表结构...")
        manager.create_database()
    
    elif args.command == 'backup':
        print("💾 备份数据库...")
        manager.backup_database(args.file)
    
    elif args.command == 'restore':
        if not args.file:
            print("❌ 请指定备份文件路径: --file backup.db")
            sys.exit(1)
        print(f"📥 恢复数据库: {args.file}")
        manager.restore_database(args.file)
    
    elif args.command == 'stats':
        manager.show_stats()
    
    elif args.command == 'clean':
        print("🧹 清理过期数据...")
        manager.clean_expired_sessions()
    
    elif args.command == 'export':
        print("📤 导出数据...")
        manager.export_data(args.file)

if __name__ == '__main__':
    main()
