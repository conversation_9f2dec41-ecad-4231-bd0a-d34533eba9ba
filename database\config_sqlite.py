"""
SQLite数据库配置和连接管理
用于在没有MySQL的情况下运行项目
"""
import os
import sqlite3
import logging
from datetime import datetime, timedelta
import hashlib
import secrets
import uuid
import threading

class DatabaseConfig:
    """SQLite数据库配置类"""
    
    def __init__(self):
        self.db_path = os.path.join(os.path.dirname(__file__), 'medical_map.db')
        self.connection = None
        self._local = threading.local()
        
        # 初始化数据库
        self.init_database()
    
    def init_database(self):
        """初始化SQLite数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute('PRAGMA foreign_keys = ON')  # 启用外键约束
            conn.close()
            logging.info("SQLite数据库初始化成功")
        except Exception as e:
            logging.error(f"SQLite数据库初始化失败: {e}")
            raise
    
    def get_connection(self):
        """获取数据库连接"""
        if not hasattr(self._local, 'connection') or self._local.connection is None:
            self._local.connection = sqlite3.connect(self.db_path)
            self._local.connection.row_factory = sqlite3.Row  # 使结果可以按列名访问
            self._local.connection.execute('PRAGMA foreign_keys = ON')
        return self._local.connection
    
    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self._local, 'connection') and self._local.connection:
            self._local.connection.close()
            self._local.connection = None

class UserManager:
    """用户管理类 - SQLite版本"""
    
    def __init__(self, db_config):
        self.db_config = db_config
    
    def create_user(self, username, email, password, user_type='patient', real_name=None):
        """创建新用户"""
        try:
            conn = self.db_config.get_connection()
            cursor = conn.cursor()
            
            # 生成用户UUID和头像信息
            user_uuid = str(uuid.uuid4())
            avatar_color = self.generate_avatar_color()
            avatar_initial = username[0].upper() if username else 'U'
            
            # 密码加密
            password_hash = self.hash_password(password)
            
            # 插入用户基础信息
            cursor.execute("""
                INSERT INTO users (uuid, username, email, password_hash, user_type, status, avatar_color, avatar_initial)
                VALUES (?, ?, ?, ?, ?, 'active', ?, ?)
            """, (user_uuid, username, email, password_hash, user_type, avatar_color, avatar_initial))
            
            user_id = cursor.lastrowid
            
            # 插入用户详细资料
            cursor.execute("""
                INSERT INTO user_profiles (user_id, real_name)
                VALUES (?, ?)
            """, (user_id, real_name or username))
            
            # 插入用户统计数据
            cursor.execute("""
                INSERT INTO user_statistics (user_id)
                VALUES (?)
            """, (user_id,))
            
            conn.commit()
            
            return {
                'success': True,
                'user_id': user_id,
                'uuid': user_uuid,
                'message': '用户创建成功'
            }
            
        except sqlite3.IntegrityError as e:
            if 'username' in str(e):
                return {'success': False, 'message': '用户名已存在'}
            elif 'email' in str(e):
                return {'success': False, 'message': '邮箱已被注册'}
            else:
                return {'success': False, 'message': '数据完整性错误'}
        except Exception as e:
            logging.error(f"创建用户失败: {e}")
            return {'success': False, 'message': '创建用户失败'}
    
    def authenticate_user(self, email, password):
        """用户认证"""
        try:
            conn = self.db_config.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, uuid, username, email, password_hash, user_type, status, 
                       avatar_color, avatar_initial, created_at
                FROM users 
                WHERE email = ? AND status = 'active'
            """, (email,))
            
            user = cursor.fetchone()
            
            if user and self.verify_password(password, user['password_hash']):
                return {
                    'success': True,
                    'user': {
                        'id': user['id'],
                        'uuid': user['uuid'],
                        'username': user['username'],
                        'email': user['email'],
                        'user_type': user['user_type'],
                        'avatar_color': user['avatar_color'],
                        'avatar_initial': user['avatar_initial'],
                        'created_at': user['created_at']
                    }
                }
            else:
                return {'success': False, 'message': '邮箱或密码错误'}
                
        except Exception as e:
            logging.error(f"用户认证失败: {e}")
            return {'success': False, 'message': '认证失败'}
    
    def create_session(self, user_id, device_info=None):
        """创建用户会话"""
        try:
            conn = self.db_config.get_connection()
            cursor = conn.cursor()
            
            session_token = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(days=7)
            
            cursor.execute("""
                INSERT INTO user_sessions (user_id, session_token, device_info, expires_at, is_active)
                VALUES (?, ?, ?, ?, 1)
            """, (user_id, session_token, device_info or 'Unknown', expires_at.isoformat()))
            
            conn.commit()
            
            return {
                'success': True,
                'session_token': session_token,
                'expires_at': expires_at.isoformat()
            }
            
        except Exception as e:
            logging.error(f"创建会话失败: {e}")
            return {'success': False, 'message': '创建会话失败'}
    
    def get_user_by_session(self, session_token):
        """通过会话令牌获取用户信息"""
        try:
            conn = self.db_config.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT u.id, u.uuid, u.username, u.email, u.user_type, 
                       u.avatar_color, u.avatar_initial, u.created_at
                FROM users u
                JOIN user_sessions s ON u.id = s.user_id
                WHERE s.session_token = ? AND s.is_active = 1 
                AND s.expires_at > ? AND u.status = 'active'
            """, (session_token, datetime.now().isoformat()))
            
            user = cursor.fetchone()
            
            if user:
                return {
                    'id': user['id'],
                    'uuid': user['uuid'],
                    'username': user['username'],
                    'email': user['email'],
                    'user_type': user['user_type'],
                    'avatar_color': user['avatar_color'],
                    'avatar_initial': user['avatar_initial'],
                    'created_at': user['created_at']
                }
            return None
            
        except Exception as e:
            logging.error(f"获取用户会话失败: {e}")
            return None
    
    def hash_password(self, password):
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"{salt}:{password_hash}"
    
    def verify_password(self, password, stored_hash):
        """验证密码"""
        try:
            salt, password_hash = stored_hash.split(':')
            return hashlib.sha256((password + salt).encode()).hexdigest() == password_hash
        except:
            return False
    
    def generate_avatar_color(self):
        """生成随机头像颜色"""
        colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ]
        return secrets.choice(colors)

# 全局数据库配置实例
db_config = DatabaseConfig()
user_manager = UserManager(db_config)
