"""
最简洁的SQLite数据库配置
"""
import os
import sqlite3
import hashlib
import secrets
import uuid
from datetime import datetime, timedelta

class DatabaseConfig:
    """简洁的SQLite数据库配置"""
    
    def __init__(self):
        self.db_path = os.path.join(os.path.dirname(__file__), 'medical_map.db')
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        if not os.path.exists(self.db_path):
            self.create_tables()
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def create_tables(self):
        """创建数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 用户表
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                user_type TEXT DEFAULT 'patient',
                avatar_color TEXT DEFAULT '#4ECDC4',
                avatar_initial TEXT DEFAULT 'U',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 用户会话表
        cursor.execute('''
            CREATE TABLE user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token TEXT UNIQUE NOT NULL,
                expires_at DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # 聊天消息表
        cursor.execute('''
            CREATE TABLE chat_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT NOT NULL,
                user_type TEXT,
                avatar_color TEXT,
                avatar_initial TEXT,
                message TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # 在线用户表
        cursor.execute('''
            CREATE TABLE chat_online_users (
                user_id INTEGER PRIMARY KEY,
                username TEXT NOT NULL,
                user_type TEXT,
                avatar_color TEXT,
                avatar_initial TEXT,
                last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        conn.commit()
        conn.close()

class UserManager:
    """简洁的用户管理类"""
    
    def __init__(self, db_config):
        self.db_config = db_config
    
    def hash_password(self, password):
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"{salt}:{password_hash}"
    
    def verify_password(self, password, stored_hash):
        """验证密码"""
        try:
            salt, password_hash = stored_hash.split(':')
            return hashlib.sha256((password + salt).encode()).hexdigest() == password_hash
        except:
            return False
    
    def generate_avatar_color(self):
        """生成随机头像颜色"""
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
        return secrets.choice(colors)
    
    def create_user(self, username, email, password, user_type='patient'):
        """创建用户"""
        try:
            conn = self.db_config.get_connection()
            cursor = conn.cursor()
            
            user_uuid = str(uuid.uuid4())
            password_hash = self.hash_password(password)
            avatar_color = self.generate_avatar_color()
            avatar_initial = username[0].upper()
            
            cursor.execute("""
                INSERT INTO users (uuid, username, email, password_hash, user_type, 
                                 avatar_color, avatar_initial)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (user_uuid, username, email, password_hash, user_type, 
                  avatar_color, avatar_initial))
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return {'success': True, 'user_id': user_id, 'message': '注册成功'}
            
        except sqlite3.IntegrityError:
            return {'success': False, 'message': '用户名或邮箱已存在'}
        except Exception as e:
            return {'success': False, 'message': f'注册失败: {str(e)}'}
    
    def authenticate_user(self, email, password):
        """用户认证"""
        try:
            conn = self.db_config.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, uuid, username, email, password_hash, user_type, 
                       avatar_color, avatar_initial, created_at
                FROM users WHERE email = ?
            """, (email,))
            
            user = cursor.fetchone()
            conn.close()
            
            if user and self.verify_password(password, user['password_hash']):
                return {
                    'success': True,
                    'user': {
                        'id': user['id'],
                        'uuid': user['uuid'],
                        'username': user['username'],
                        'email': user['email'],
                        'user_type': user['user_type'],
                        'avatar_color': user['avatar_color'],
                        'avatar_initial': user['avatar_initial'],
                        'created_at': user['created_at']
                    }
                }
            return {'success': False, 'message': '邮箱或密码错误'}
            
        except Exception as e:
            return {'success': False, 'message': f'登录失败: {str(e)}'}
    
    def create_session(self, user_id):
        """创建会话"""
        try:
            conn = self.db_config.get_connection()
            cursor = conn.cursor()
            
            session_token = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(days=7)
            
            cursor.execute("""
                INSERT INTO user_sessions (user_id, session_token, expires_at)
                VALUES (?, ?, ?)
            """, (user_id, session_token, expires_at.isoformat()))
            
            conn.commit()
            conn.close()
            
            return {'success': True, 'session_token': session_token}
            
        except Exception as e:
            return {'success': False, 'message': f'创建会话失败: {str(e)}'}
    
    def get_user_by_session(self, session_token):
        """通过会话获取用户"""
        try:
            conn = self.db_config.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT u.id, u.uuid, u.username, u.email, u.user_type, 
                       u.avatar_color, u.avatar_initial, u.created_at
                FROM users u
                JOIN user_sessions s ON u.id = s.user_id
                WHERE s.session_token = ? AND s.expires_at > ?
            """, (session_token, datetime.now().isoformat()))
            
            user = cursor.fetchone()
            conn.close()
            
            if user:
                return {
                    'id': user['id'],
                    'uuid': user['uuid'],
                    'username': user['username'],
                    'email': user['email'],
                    'user_type': user['user_type'],
                    'avatar_color': user['avatar_color'],
                    'avatar_initial': user['avatar_initial'],
                    'created_at': user['created_at']
                }
            return None
            
        except Exception:
            return None

# 全局实例
db_config = DatabaseConfig()
user_manager = UserManager(db_config)
