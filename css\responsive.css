/* 响应式设计 */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 350px 1fr;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 2.2rem;
    }

    .footer-sections {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .gov-header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .gov-links {
        gap: 15px;
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .header p {
        font-size: 1rem;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
        min-height: auto;
    }

    .sidebar {
        order: 2;
        height: auto;
        max-height: 600px;
    }

    .map-container {
        order: 1;
        height: 400px;
        min-height: 400px;
    }

    .hospital-list {
        max-height: 250px;
    }

    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .footer-sections {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .social-links {
        justify-content: center;
    }
}