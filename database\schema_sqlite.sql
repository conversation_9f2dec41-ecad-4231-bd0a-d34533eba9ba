-- SQLite版本的数据库表结构
-- 医疗专长地图用户数据库

-- 1. 用户基础信息表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    user_type TEXT CHECK(user_type IN ('patient', 'doctor', 'researcher', 'admin')) DEFAULT 'patient',
    status TEXT CHECK(status IN ('active', 'inactive', 'suspended', 'deleted')) DEFAULT 'active',
    avatar_color TEXT DEFAULT '#4ECDC4',
    avatar_initial TEXT DEFAULT 'U',
    last_login_at DATETIME,
    login_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 用户详细资料表
CREATE TABLE IF NOT EXISTS user_profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    real_name TEXT,
    avatar_url TEXT,
    bio TEXT,
    location TEXT,
    profession TEXT,
    specialties TEXT, -- JSON格式存储专长列表
    experience TEXT,
    verified INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 3. 用户统计数据表
CREATE TABLE IF NOT EXISTS user_statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    favorites_count INTEGER DEFAULT 0,
    questions_asked INTEGER DEFAULT 0,
    helpful_votes INTEGER DEFAULT 0,
    profile_views INTEGER DEFAULT 0,
    last_activity_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 4. 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_token TEXT UNIQUE NOT NULL,
    device_info TEXT,
    ip_address TEXT,
    user_agent TEXT,
    expires_at DATETIME NOT NULL,
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 5. 用户操作日志表
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action_type TEXT NOT NULL,
    action_detail TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 6. 用户收藏医院表
CREATE TABLE IF NOT EXISTS user_favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    hospital_name TEXT NOT NULL,
    hospital_address TEXT,
    specialty TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 7. 用户消息表
CREATE TABLE IF NOT EXISTS user_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sender_id INTEGER,
    receiver_id INTEGER NOT NULL,
    subject TEXT,
    content TEXT NOT NULL,
    message_type TEXT CHECK(message_type IN ('system', 'user', 'notification')) DEFAULT 'user',
    is_read INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 8. 用户设置表
CREATE TABLE IF NOT EXISTS user_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    setting_key TEXT NOT NULL,
    setting_value TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, setting_key)
);

-- 9. 实时聊天消息表
CREATE TABLE IF NOT EXISTS chat_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    username TEXT NOT NULL,
    user_type TEXT CHECK(user_type IN ('patient', 'doctor', 'researcher', 'admin')),
    avatar_color TEXT,
    avatar_initial TEXT,
    message TEXT NOT NULL,
    message_type TEXT CHECK(message_type IN ('text', 'system', 'join', 'leave')) DEFAULT 'text',
    is_deleted INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 10. 聊天室在线用户表
CREATE TABLE IF NOT EXISTS chat_online_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    username TEXT NOT NULL,
    user_type TEXT CHECK(user_type IN ('patient', 'doctor', 'researcher', 'admin')),
    avatar_color TEXT,
    avatar_initial TEXT,
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_type ON chat_messages(message_type);

CREATE INDEX IF NOT EXISTS idx_chat_online_users_last_seen ON chat_online_users(last_seen);

CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_created_at ON user_activity_logs(created_at);

-- 插入初始数据
INSERT OR IGNORE INTO users (uuid, username, email, password_hash, user_type, avatar_color, avatar_initial) 
VALUES 
('admin-uuid-001', 'admin', '<EMAIL>', 'salt123:hashedpassword', 'admin', '#FF6B6B', 'A'),
('demo-patient-001', 'patient_demo', '<EMAIL>', 'salt456:hashedpassword', 'patient', '#4ECDC4', 'P'),
('demo-doctor-001', 'doctor_demo', '<EMAIL>', 'salt789:hashedpassword', 'doctor', '#45B7D1', 'D');

-- 插入对应的用户资料
INSERT OR IGNORE INTO user_profiles (user_id, real_name) 
VALUES 
(1, '系统管理员'),
(2, '演示患者'),
(3, '演示医生');

-- 插入对应的用户统计
INSERT OR IGNORE INTO user_statistics (user_id) 
VALUES (1), (2), (3);

-- 插入一些演示聊天消息
INSERT OR IGNORE INTO chat_messages (user_id, username, user_type, avatar_color, avatar_initial, message, message_type)
VALUES 
(1, 'admin', 'admin', '#FF6B6B', 'A', '欢迎来到医疗专长地图聊天室！', 'system'),
(2, 'patient_demo', 'patient', '#4ECDC4', 'P', '大家好，我是新用户，请多指教！', 'text'),
(3, 'doctor_demo', 'doctor', '#45B7D1', 'D', '欢迎！有任何医疗问题都可以在这里交流。', 'text');
