// UI管理模块
class UIManager {
    constructor(dataManager, mapManager) {
        this.dataManager = dataManager;
        this.mapManager = mapManager;
        this.tooltip = null;
        this.selectedHospitals = new Set(); // 收藏功能
        this.initTooltip();
    }

    initTooltip() {
        // 创建悬浮提示框
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'hospital-tooltip';
        document.body.appendChild(this.tooltip);
    }

    updateHospitalList() {
        const hospitalList = document.getElementById('hospitalList');
        const data = this.dataManager.getFilteredData();
        
        hospitalList.innerHTML = '';

        if (data.length === 0) {
            hospitalList.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #666;">
                    <p>没有找到匹配的医院</p>
                </div>
            `;
            return;
        }

        data.forEach((hospital, index) => {
            const card = this.createHospitalCard(hospital, index);
            hospitalList.appendChild(card);
        });
    }

    createHospitalCard(hospital, index) {
        const card = document.createElement('div');
        card.className = 'hospital-card';
        card.style.animationDelay = `${index * 0.1}s`;
        
        const isBookmarked = this.selectedHospitals.has(hospital.id);
        
        card.innerHTML = `
            <div class="hospital-name">${hospital.name}</div>
            <div class="hospital-location">📍 ${hospital.province} · ${hospital.city}</div>
            <div class="hospital-specialty">${hospital.specialty}</div>
            <div style="margin-top: 8px; display: flex; justify-content: space-between; align-items: center;">
                <button class="bookmark-btn ${isBookmarked ? 'bookmarked' : ''}" 
                        onclick="app.uiManager.toggleBookmark(${hospital.id})" 
                        title="${isBookmarked ? '取消收藏' : '收藏医院'}">
                    ${isBookmarked ? '❤️' : '🤍'}
                </button>
                <button class="share-btn" onclick="app.uiManager.shareHospital(${hospital.id})" title="分享医院">
                    📤
                </button>
            </div>
        `;

        // 悬浮详情功能
        card.addEventListener('mouseenter', (e) => {
            this.showTooltip(hospital, e);
        });

        card.addEventListener('mouseleave', () => {
            this.hideTooltip();
        });

        card.addEventListener('mousemove', (e) => {
            this.updateTooltipPosition(e);
        });

        card.addEventListener('click', () => {
            this.mapManager.focusOnHospital(hospital.id);
        });

        return card;
    }

    showTooltip(hospital, event) {
        this.tooltip.innerHTML = `
            <div class="tooltip-header">
                <div class="tooltip-title">${hospital.name}</div>
                <div class="tooltip-location">📍 ${hospital.province} ${hospital.city}</div>
            </div>
            <div class="tooltip-content">
                <div style="margin-bottom: 8px;">
                    <span class="tooltip-specialty-tag">${hospital.category}</span>
                </div>
                <div style="margin-bottom: 8px;">
                    <strong>专科领域：</strong>${hospital.specialty}
                </div>
                <div style="margin-bottom: 8px;">
                    <strong>收录依据：</strong>${hospital.basis}
                </div>
                <div>
                    <strong>备注信息：</strong>${hospital.notes}
                </div>
            </div>
        `;
        
        this.updateTooltipPosition(event);
        this.tooltip.classList.add('show');
    }

    hideTooltip() {
        this.tooltip.classList.remove('show');
    }

    updateTooltipPosition(event) {
        const tooltipRect = this.tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        let x = event.clientX + 15;
        let y = event.clientY + 15;
        
        // 防止提示框超出视口
        if (x + tooltipRect.width > viewportWidth) {
            x = event.clientX - tooltipRect.width - 15;
        }
        if (y + tooltipRect.height > viewportHeight) {
            y = event.clientY - tooltipRect.height - 15;
        }
        
        this.tooltip.style.left = `${x}px`;
        this.tooltip.style.top = `${y}px`;
    }

    updateFilterButtons() {
        const filterButtons = document.getElementById('filterButtons');
        const categories = this.dataManager.getCategories();
        
        const categoryEmojis = {
            '中毒救治': '☠️',
            '高原病': '🏔️',
            '包虫病': '🦠',
            '职业病': '🏭',
            '海洋生物': '🌊',
            '其他': '📋'
        };
        
        filterButtons.innerHTML = `
            <button class="filter-btn active" data-filter="all">🌟 全部</button>
            ${categories.map(category => 
                `<button class="filter-btn" data-filter="${category}">${categoryEmojis[category] || '📋'} ${category}</button>`
            ).join('')}
            <button class="filter-btn special-btn" onclick="app.uiManager.toggleHeatmap()" title="热力图模式">
                🔥 热力图
            </button>
            <button class="filter-btn special-btn" onclick="app.uiManager.toggleConnections()" title="专科连线">
                🔗 连线
            </button>
        `;
        
        // 绑定筛选事件
        filterButtons.querySelectorAll('.filter-btn[data-filter]').forEach(btn => {
            btn.addEventListener('click', () => this.handleFilterClick(btn));
        });
    }

    handleFilterClick(btn) {
        // 更新按钮状态
        document.querySelectorAll('.filter-btn[data-filter]').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        
        // 执行筛选
        const filter = btn.getAttribute('data-filter');
        this.dataManager.filterByCategory(filter);
        this.updateUI();
    }

    updateStats() {
        const stats = this.dataManager.getStats();
        
        document.getElementById('totalHospitals').textContent = stats.totalHospitals;
        document.getElementById('totalSpecialties').textContent = stats.totalSpecialties;
        document.getElementById('totalProvinces').textContent = stats.totalProvinces;
        document.getElementById('totalCities').textContent = stats.totalCities;
    }

    setupEventListeners() {
        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', (e) => {
            this.dataManager.filterBySearch(e.target.value);
            this.updateUI();
        });
    }

    updateUI() {
        this.updateHospitalList();
        this.updateStats();
        this.mapManager.updateMarkers();
    }

    // 地域知识卡片功能
    showRegionKnowledge(province) {
        const knowledgeData = {
            '云南': {
                title: '云南医疗特色',
                content: '云南因地处亚热带高原，野生菌类丰富，导致误食中毒事件频发。当地医院在野生菌中毒救治方面积累了丰富经验，形成了独特的诊疗体系。',
                specialties: ['野生菌中毒救治', '高原反应治疗', '热带病防治']
            },
            '西藏': {
                title: '西藏高原医学',
                content: '西藏平均海拔4000米以上，是世界屋脊。当地医疗机构在高原病诊治、藏医药应用等方面具有不可替代的优势。',
                specialties: ['高原病科', '藏医心血管', '藏医脑病科']
            },
            '新疆': {
                title: '新疆牧区医疗',
                content: '新疆地域辽阔，畜牧业发达，包虫病等人畜共患病较为常见。当地医院在包虫病诊治方面积累了丰富经验。',
                specialties: ['包虫病治疗', '牧区常见病', '民族医学']
            }
        };

        const data = knowledgeData[province];
        if (data) {
            this.showModal('地域医疗知识', `
                <div style="text-align: left;">
                    <h3 style="color: #0052cc; margin-bottom: 16px;">${data.title}</h3>
                    <p style="line-height: 1.6; margin-bottom: 16px;">${data.content}</p>
                    <h4 style="color: #333; margin-bottom: 12px;">主要专科特色：</h4>
                    <ul style="padding-left: 20px;">
                        ${data.specialties.map(s => `<li style="margin-bottom: 4px;">${s}</li>`).join('')}
                    </ul>
                </div>
            `);
        }
    }

    // 收藏功能
    toggleBookmark(hospitalId) {
        if (this.selectedHospitals.has(hospitalId)) {
            this.selectedHospitals.delete(hospitalId);
        } else {
            this.selectedHospitals.add(hospitalId);
        }
        
        // 保存到本地存储
        localStorage.setItem('bookmarkedHospitals', JSON.stringify([...this.selectedHospitals]));
        
        // 更新UI
        this.updateHospitalList();
        
        // 显示提示
        const hospital = this.dataManager.getAllData().find(h => h.id === hospitalId);
        if (hospital) {
            this.showToast(this.selectedHospitals.has(hospitalId) ? 
                `已收藏 ${hospital.name}` : `已取消收藏 ${hospital.name}`);
        }
    }

    // 分享功能
    shareHospital(hospitalId) {
        const hospital = this.dataManager.getAllData().find(h => h.id === hospitalId);
        if (hospital) {
            const shareText = `${hospital.name} - ${hospital.specialty}\n位置：${hospital.province} ${hospital.city}\n详情：${window.location.href}#hospital=${hospitalId}`;
            
            if (navigator.share) {
                navigator.share({
                    title: hospital.name,
                    text: shareText,
                    url: window.location.href
                });
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareText).then(() => {
                    this.showToast('医院信息已复制到剪贴板');
                });
            }
        }
    }

    // 特殊功能按钮
    toggleHeatmap() {
        this.mapManager.toggleHeatmap();
        const btn = document.querySelector('.filter-btn[onclick="app.uiManager.toggleHeatmap()"]');
        btn.classList.toggle('active');
        this.showToast(this.mapManager.showHeatmap ? '已开启热力图模式' : '已关闭热力图模式');
    }

    toggleConnections() {
        this.mapManager.toggleConnections();
        const btn = document.querySelector('.filter-btn[onclick="app.uiManager.toggleConnections()"]');
        btn.classList.toggle('active');
        this.showToast(this.mapManager.showConnections ? '已显示专科连线' : '已隐藏专科连线');
    }

    // 工具方法
    showModal(title, content) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
            background: rgba(0,0,0,0.5); z-index: 10000; display: flex; 
            align-items: center; justify-content: center;
        `;
        
        modal.innerHTML = `
            <div style="background: white; padding: 24px; border-radius: 8px; max-width: 500px; margin: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <h3 style="margin: 0; color: #333;">${title}</h3>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                            style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
                </div>
                ${content}
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });
    }

    showToast(message) {
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed; top: 80px; right: 20px; background: #0052cc; 
            color: white; padding: 12px 20px; border-radius: 4px; z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15); opacity: 0; transition: opacity 0.3s;
        `;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.style.opacity = '1', 100);
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // 加载收藏数据
    loadBookmarks() {
        const saved = localStorage.getItem('bookmarkedHospitals');
        if (saved) {
            this.selectedHospitals = new Set(JSON.parse(saved));
        }
    }

    // 医院详情
    showHospitalDetails(hospitalId) {
        const hospital = this.dataManager.getAllData().find(h => h.id == hospitalId);
        if (hospital) {
            this.showModal(hospital.name, `
                <div style="text-align: left;">
                    <div style="margin-bottom: 16px;">
                        <span style="background: #0052cc; color: white; padding: 4px 8px; border-radius: 3px; font-size: 12px;">
                            ${hospital.category}
                        </span>
                    </div>
                    <div style="margin-bottom: 12px;">
                        <strong>📍 位置：</strong>${hospital.province} ${hospital.city}
                    </div>
                    <div style="margin-bottom: 12px;">
                        <strong>🏥 专科：</strong>${hospital.specialty}
                    </div>
                    <div style="margin-bottom: 12px;">
                        <strong>📋 依据：</strong>${hospital.basis}
                    </div>
                    <div style="margin-bottom: 16px;">
                        <strong>📝 备注：</strong>${hospital.notes}
                    </div>
                    <div style="text-align: center;">
                        <button onclick="app.mapManager.focusOnHospital(${hospital.id}); this.parentElement.parentElement.parentElement.parentElement.remove();" 
                                style="background: #0052cc; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            在地图上查看
                        </button>
                    </div>
                </div>
            `);
        }
    }
}

// 导出模块
window.UIManager = UIManager;