#!/usr/bin/env python3
"""
数据库管理工具
用于初始化数据库、备份、恢复等操作
"""
import os
import sys
import argparse
import subprocess
import mysql.connector
from datetime import datetime
import json

from config import db_config

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.config = db_config.config
    
    def create_database(self):
        """创建数据库"""
        try:
            # 连接MySQL服务器（不指定数据库）
            config = self.config.copy()
            config.pop('database', None)
            
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()
            
            # 创建数据库
            cursor.execute(f"""
                CREATE DATABASE IF NOT EXISTS {self.config['database']} 
                CHARACTER SET utf8mb4 
                COLLATE utf8mb4_unicode_ci
            """)
            
            print(f"数据库 {self.config['database']} 创建成功")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"创建数据库失败: {e}")
            return False
        
        return True
    
    def init_schema(self):
        """初始化数据库结构"""
        try:
            # 执行schema.sql
            self.execute_sql_file('schema.sql')
            print("数据库结构初始化成功")
            return True
            
        except Exception as e:
            print(f"初始化数据库结构失败: {e}")
            return False
    
    def init_data(self):
        """初始化数据"""
        try:
            # 执行init_data.sql
            self.execute_sql_file('init_data.sql')
            print("初始数据插入成功")
            return True
            
        except Exception as e:
            print(f"初始化数据失败: {e}")
            return False
    
    def execute_sql_file(self, filename):
        """执行SQL文件"""
        filepath = os.path.join(os.path.dirname(__file__), filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"SQL文件不存在: {filepath}")
        
        conn = mysql.connector.connect(**self.config)
        cursor = conn.cursor()
        
        with open(filepath, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        statements = sql_content.split(';')
        
        for statement in statements:
            statement = statement.strip()
            if statement and not statement.startswith('--'):
                try:
                    cursor.execute(statement)
                except Exception as e:
                    print(f"执行SQL语句失败: {statement[:50]}...")
                    print(f"错误: {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
    
    def backup_database(self, backup_path=None):
        """备份数据库"""
        if not backup_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"backup_{self.config['database']}_{timestamp}.sql"
        
        try:
            cmd = [
                'mysqldump',
                f"--host={self.config['host']}",
                f"--port={self.config['port']}",
                f"--user={self.config['user']}",
                f"--password={self.config['password']}",
                '--single-transaction',
                '--routines',
                '--triggers',
                self.config['database']
            ]
            
            with open(backup_path, 'w') as f:
                subprocess.run(cmd, stdout=f, check=True)
            
            print(f"数据库备份成功: {backup_path}")
            return True
            
        except Exception as e:
            print(f"数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_path):
        """恢复数据库"""
        if not os.path.exists(backup_path):
            print(f"备份文件不存在: {backup_path}")
            return False
        
        try:
            cmd = [
                'mysql',
                f"--host={self.config['host']}",
                f"--port={self.config['port']}",
                f"--user={self.config['user']}",
                f"--password={self.config['password']}",
                self.config['database']
            ]
            
            with open(backup_path, 'r') as f:
                subprocess.run(cmd, stdin=f, check=True)
            
            print(f"数据库恢复成功: {backup_path}")
            return True
            
        except Exception as e:
            print(f"数据库恢复失败: {e}")
            return False
    
    def show_stats(self):
        """显示数据库统计信息"""
        try:
            conn = mysql.connector.connect(**self.config)
            cursor = conn.cursor(dictionary=True)
            
            # 用户统计
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN user_type = 'patient' THEN 1 ELSE 0 END) as patients,
                    SUM(CASE WHEN user_type = 'doctor' THEN 1 ELSE 0 END) as doctors,
                    SUM(CASE WHEN user_type = 'researcher' THEN 1 ELSE 0 END) as researchers,
                    SUM(CASE WHEN user_type = 'admin' THEN 1 ELSE 0 END) as admins
                FROM users WHERE status = 'active'
            """)
            user_stats = cursor.fetchone()
            
            # 今日注册统计
            cursor.execute("""
                SELECT COUNT(*) as today_registrations
                FROM users 
                WHERE DATE(created_at) = CURDATE()
            """)
            today_stats = cursor.fetchone()
            
            # 活跃会话统计
            cursor.execute("""
                SELECT COUNT(*) as active_sessions
                FROM user_sessions 
                WHERE is_active = TRUE AND expires_at > NOW()
            """)
            session_stats = cursor.fetchone()
            
            print("\n=== 数据库统计信息 ===")
            print(f"总用户数: {user_stats['total_users']}")
            print(f"  - 患者: {user_stats['patients']}")
            print(f"  - 医生: {user_stats['doctors']}")
            print(f"  - 研究员: {user_stats['researchers']}")
            print(f"  - 管理员: {user_stats['admins']}")
            print(f"今日注册: {today_stats['today_registrations']}")
            print(f"活跃会话: {session_stats['active_sessions']}")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"获取统计信息失败: {e}")
    
    def clean_expired_sessions(self):
        """清理过期会话"""
        try:
            conn = mysql.connector.connect(**self.config)
            cursor = conn.cursor()
            
            cursor.execute("""
                DELETE FROM user_sessions 
                WHERE expires_at < NOW() OR is_active = FALSE
            """)
            
            deleted_count = cursor.rowcount
            conn.commit()
            
            print(f"清理过期会话成功，删除 {deleted_count} 条记录")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"清理过期会话失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='数据库管理工具')
    parser.add_argument('command', choices=[
        'create', 'init', 'backup', 'restore', 'stats', 'clean'
    ], help='要执行的命令')
    parser.add_argument('--file', help='备份/恢复文件路径')
    
    args = parser.parse_args()
    
    manager = DatabaseManager()
    
    if args.command == 'create':
        print("创建数据库...")
        if manager.create_database():
            print("初始化数据库结构...")
            if manager.init_schema():
                print("插入初始数据...")
                manager.init_data()
    
    elif args.command == 'init':
        print("初始化数据库结构...")
        if manager.init_schema():
            print("插入初始数据...")
            manager.init_data()
    
    elif args.command == 'backup':
        print("备份数据库...")
        manager.backup_database(args.file)
    
    elif args.command == 'restore':
        if not args.file:
            print("请指定备份文件路径: --file backup.sql")
            sys.exit(1)
        print(f"恢复数据库: {args.file}")
        manager.restore_database(args.file)
    
    elif args.command == 'stats':
        manager.show_stats()
    
    elif args.command == 'clean':
        print("清理过期会话...")
        manager.clean_expired_sessions()

if __name__ == '__main__':
    main()
