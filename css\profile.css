/* 个人中心页面样式 */
.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: calc(100vh - 200px);
}

/* 个人资料头部 */
.profile-header {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 30px;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #667eea;
    position: relative;
}

.profile-avatar .avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 36px;
}

.profile-avatar .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info {
    flex: 1;
}

.profile-info h1 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 700;
}

.profile-info p {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 1.1rem;
}

.profile-meta {
    display: flex;
    gap: 20px;
    font-size: 0.9rem !important;
    color: #999 !important;
}

.profile-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 主要内容区域 */
.profile-content {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 30px;
}

/* 侧边栏 */
.profile-sidebar {
    background: white;
    border-radius: 12px;
    padding: 20px 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background: #f8f9fa;
    color: #667eea;
}

.menu-item.active {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border-left-color: #667eea;
}

.menu-icon {
    font-size: 18px;
    width: 20px;
    text-align: center;
}

/* 主要内容 */
.profile-main {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.content-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-item label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.info-item span {
    color: #666;
    font-size: 16px;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.9;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 收藏列表 */
.favorites-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.favorite-item {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
}

.favorite-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

/* 消息相关样式 */
.message-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.filter-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.filter-btn:hover:not(.active) {
    border-color: #667eea;
    color: #667eea;
}

.messages-list {
    min-height: 200px;
}

.message-item {
    padding: 16px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background 0.3s ease;
    border-radius: 8px;
    margin-bottom: 8px;
}

.message-item:hover {
    background: #f8f9fa;
}

.message-item.unread {
    background: rgba(102, 126, 234, 0.05);
    border-left: 3px solid #667eea;
    font-weight: 500;
}

.message-item.important {
    border-left: 3px solid #ff9800;
}

.message-item.unread.important {
    border-left: 3px solid #ff5722;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.message-sender {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
}

.message-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
}

.message-avatar .avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    font-weight: 600;
}

.message-time {
    color: #999;
    font-size: 12px;
}

.message-content h5 {
    margin: 0 0 4px 0;
    color: #333;
    font-size: 14px;
    font-weight: 500;
}

.message-content p {
    margin: 0;
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

.message-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.message-item:hover .message-actions {
    opacity: 1;
}

.message-action-btn {
    padding: 4px 8px;
    background: none;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.message-action-btn:hover {
    background: #f0f0f0;
}

.message-action-btn.reply {
    color: #667eea;
    border-color: #667eea;
}

.message-action-btn.delete {
    color: #e74c3c;
    border-color: #e74c3c;
}

/* 消息撰写模态框样式 */
.recipient-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.suggestion-item:hover {
    background: #f8f9fa;
}

.suggestion-avatar {
    flex-shrink: 0;
}

.suggestion-info {
    flex: 1;
}

.suggestion-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.suggestion-type {
    color: #666;
    font-size: 12px;
}

.character-count {
    text-align: right;
    margin-top: 4px;
    font-size: 12px;
    color: #666;
}

.form-options {
    margin-top: 16px;
}

/* 消息状态标识 */
.message-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #666;
}

.status-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-icon.unread {
    background: #667eea;
}

.status-icon.important {
    background: #ff9800;
}

.status-icon.sent {
    background: #4CAF50;
}

.status-icon.replied {
    background: #9C27B0;
}

/* 消息详情页面 */
.message-detail {
    display: none;
    padding: 20px;
    background: white;
    border-radius: 8px;
    margin-top: 20px;
}

.message-detail.active {
    display: block;
}

.message-detail-header {
    padding-bottom: 16px;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.message-detail-subject {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
}

.message-detail-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #666;
    font-size: 14px;
}

.message-detail-content {
    line-height: 1.6;
    color: #333;
    white-space: pre-wrap;
}

.message-detail-actions {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 12px;
}

/* 回复框 */
.reply-box {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    display: none;
}

.reply-box.active {
    display: block;
}

.reply-textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    resize: vertical;
    font-family: inherit;
}

.reply-actions {
    margin-top: 12px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* 设置部分 */
.settings-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.settings-section {
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
}

.setting-info {
    flex: 1;
}

.setting-info label {
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 4px;
}

.setting-info p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* 开关组件 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
}

.modal-header {
    padding: 20px 30px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #666;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 按钮样式 */
.btn-primary, .btn-secondary, .btn-text, .btn-danger {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.btn-text {
    background: none;
    color: #667eea;
    padding: 8px 12px;
}

.btn-text:hover {
    background: rgba(102, 126, 234, 0.1);
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .profile-container {
        padding: 10px;
    }
    
    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .profile-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .profile-sidebar {
        order: 2;
    }
    
    .sidebar-menu {
        flex-direction: row;
        overflow-x: auto;
    }
    
    .menu-item {
        min-width: 120px;
        text-align: center;
        flex-direction: column;
        gap: 8px;
        padding: 12px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-body,
    .modal-header,
    .modal-footer {
        padding: 20px;
    }
}